import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/config/bottom_nav_config.dart';

import 'package:wd/core/models/apis/home.dart';
import 'package:wd/core/models/apis/lobby_api.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/jump_model.dart';
import 'package:wd/core/models/view_models/popular_section_view_model.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/game_recently_util.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/polling_services/polliing_services.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/core/utils/dialog_queue_manager/dialog_queue_manager.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:flutter/material.dart';

import '../../../shared/widgets/home/<USER>/home_expandable_activity_card.dart';
import 'game_home_state.dart';

class GameHomeCubit extends Cubit<GameHomeState> {
  PageController? popularPageController;

  GameHomeCubit() : super(const GameHomeState());

  void fetchData({bool forcedUpdate = false}) {
    fetchPopularAndFavList();
    fetchGameList();
    if (forcedUpdate || state.bannerList.isEmpty) {
      fetchBannerList();
    }
    if (forcedUpdate || state.marqueeList.isEmpty) {
        fetchDialogNMarqueeListList();
    }
    if (forcedUpdate || state.activities.isEmpty) {
      fetchActivityList();
    }

    fetchActivityActionList();
  }

  /// 开启奖金池轮询，仅在游戏首页生效
  startBonusPoolPolling() {
    sl<PollingService>().startPolling(
        id: kGSGameHomeBonusPool,
        keepAlive: true,
        onPoll: () async {
          final res = await LobbyApi.getBonusPool();
          emit(state.copyWith(bonusPool: res));
          return true;
        },
        interval: const Duration(seconds: 5),
        shouldPause: () =>
            RouteTracker().getCurrentRouteName() != AppRouter.nav ||
            sl<MainScreenCubit>().state.currentTabType != BottomNavType.gameHome);
  }

  void initPopularControllers(TickerProvider vsync) {
    if (popularPageController != null) {
      // 先将控制器从 UI 中移除
      popularPageController?.dispose();
      popularPageController = null;
      emit(state.copyWith(currentPopularIndex: 0));
    }

    popularPageController = PageController(
      initialPage: state.currentPopularIndex,
    );
  }

  void onTabPageChanging(int index) {
    popularPageController?.jumpToPage(index);
    emit(state.copyWith(
      currentPopularIndex: index,
      needUpdatePopularHeight: true,
    ));
  }

  void onPopularPageChanged(int index) {
    emit(state.copyWith(currentPopularIndex: index));
  }

  @override
  Future<void> close() {
    popularPageController?.dispose();
    return super.close();
  }

  fetchRecentlyPlayList() async {
    GameRecentlyUtil().addListener(() {
      _updateRecentlyModel();
    });
    _updateRecentlyModel();
  }

  void _updateRecentlyModel() {
    final recentlyModel = PopularSectionViewModel(
      gameList: GameRecentlyUtil().gameList,
      venueList: GameRecentlyUtil().platformList,
    );
    emit(state.copyWith(recentlyModel: recentlyModel));
  }

  fetchPopularAndFavList() async {
    final res = await GameUtil().fetchFavList();
    if (res.isNotEmpty) {
      GameRecentlyUtil().onChangeGameFav();

      /// 收藏
      final newFavModel = PopularSectionViewModel(gameList: res);
      // 使用 copyWith 创建新的状态
      final newState = state.copyWith(
        favModel: newFavModel,
        // 添加一个时间戳或其他变化值，确保状态更新
        needUpdatePopularHeight: !state.needUpdatePopularHeight,
      );
      emit(newState);
    }
  }

  fetchGameList() async {
    try {
      // 请求数据
      List<GameTypeV2> list = await GameUtil().fetchGameList<GameTypeV2>();

      GameTypeV2? popularType = list.firstWhereOrNull((e) => e.type == 3);
      if (popularType != null) {
        final newPopularModel = PopularSectionViewModel(
          gameList: popularType.games,
          venueList: const [], // 新需求 热门不展示场馆
        );
        emit(state.copyWith(popularModel: newPopularModel));
      }

      emit(state.copyWith(
        gameTypeList: list,
        sectionHeights: list.map((e) {
          if (e.type == 3) return 0.0;
          return e.sectionHeight;
        }).toList(),
      ));
    } catch (e) {
      emit(state.copyWith(netState: NetState.showReload));
    } finally {
      emit(state.copyWith(netState: state.gameTypeList.isEmpty ? NetState.showReload : NetState.success));
      if (state.gameTypeList.isEmpty) {
        // 如果缓存数据存在，直接更新状态
        if (GameUtil().gameList.isNotEmpty) {
          emit(state.copyWith(
            netState: NetState.success,
            gameTypeList: GameUtil().gameList,
          ));
        }
      }
    }
  }

  /// 点击红包
  Future<bool> checkRedPacketEligibility() async {
    GSEasyLoading.showLoading();
    final isEligible = await LobbyApi.checkLobbyRedPacketEligibility();
    GSEasyLoading.dismiss();

    if (!isEligible) {
      GSEasyLoading.showToast('no_red_packet_to_claim'.tr());
    }
    return isEligible;
  }


  /// 获取轮播图
  void fetchBannerList() async {
    final list = await HomeApi.fetchHomeBannerList();
    emit(state.copyWith(bannerList: list));
  }

  /// 获取活动列表 （伸缩栏）
  void fetchActivityList() async {
    final now = DateTime.now();
    emit(state.copyWith(activities: [
      ActivityItem(
        id: '1',
        title: 'LUCKY',
        imageUrl: 'assets/images/home/<USER>/icon_lucky.png',
        endTime: now.add(const Duration(hours: 2, minutes: 30, seconds: 45)),
        onTap: () => DialogQueueManager().showDialogByType(DialogType.luckyGift),
      ),
      ActivityItem(
        id: '2',
        title: 'HOT',
        imageUrl: 'assets/images/home/<USER>/icon_hot.png',
        onTap: () => DialogQueueManager().showDialogByType(DialogType.popular),
      ),
      ActivityItem(
        id: '3',
        title: 'NEWS',
        imageUrl: 'assets/images/home/<USER>/icon_news.png',
        onTap: () => DialogQueueManager().showDialogByType(DialogType.notice),
      ),
      ActivityItem(
        id: '4',
        title: 'BONUS',
        imageUrl: 'assets/images/home/<USER>/icon_bonus.png',
        onTap: () => _onActivityTap('BONUS'),
      ),
    ]));
  }

  void _onActivityTap(String activityName) {
    final context = sl<NavigatorService>().navigatorKey.currentState!.context;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('点击了 $activityName 活动')),
    );
  }

  /// 用户点击banner
  void userDidClickBanner(int index) {
    final model = state.bannerList[index];
    final jumpModel = JumpModel.fromHomeBannerEntity(model);

    LogD("jumpModel>>>> ${jumpModel.toJson()}");
    SystemUtil.onJump(jumpModel);
  }

  /// 点击平台（品牌）
  onClickPlatformCell({required dynamic gameType, required dynamic platform}) {
    onClickPlatformCellV2(gameType: gameType, platform: platform);
  }

  onClickPlatformCellV2({required GameTypeV2 gameType, required GamePlatformV2 platform}) {
    if (platform.isGame == 1) {
      /// isGame == 1， 是游戏，直接登录
      AuthUtil.checkIfLogin(() {
        GameUtil().fetchGameLoginData(
          gameId: 0,
          platformId: platform.id,
        );
      });
    } else {
      sl<NavigatorService>().push(AppRouter.gameListV2, arguments: {"gameType": gameType});
    }
  }

  jumpToDetailPage({required GameTypeV2 gameType}) {
    sl<NavigatorService>().push(AppRouter.gameListV2, arguments: {"gameType": gameType});
  }

  /// 获取弹窗&文字走马灯列表
  void fetchDialogNMarqueeListList() async {
    final res = await HomeApi.fetchHomeFeedList();
    if (res != null) {
      // 使用DialogQueueManager处理弹窗队列
      await DialogQueueManager().processDialogQueue(res);
    }

    emit(state.copyWith(marqueeList: res?.marqueeList));
  }

  void fetchActivityActionList() async {
    final res = await HomeApi.fetchHomeActivityActionList();
    if (res != null) {
      await Future.delayed(const Duration(milliseconds: 500));
    }

    emit(state.copyWith(marqueeList: res?.marqueeList));
  }

  onChangeCurrentTabIndex(int index) {
    if (state.currentTabIndex != index) {
      emit(state.copyWith(currentTabIndex: index));
    }
  }
}
