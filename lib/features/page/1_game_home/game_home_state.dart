import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/bonus_pool_entity.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/home_banner_entity.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/models/view_models/popular_section_view_model.dart';
import 'package:wd/shared/widgets/home/<USER>/home_expandable_activity_card.dart';

class GameHomeState extends Equatable {
  final int currentTabIndex;
  final int currentPopularIndex;
  final List<HomeFeedItem> marqueeList;
  final List<HomeBannerEntity> bannerList;
  final List<GameTypeV2> gameTypeList;
  final List<double> sectionHeights;
  final List<ActivityItem> activities;
  final NetState netState;

  final PopularSectionViewModel? popularModel;
  final PopularSectionViewModel? recentlyModel;
  final PopularSectionViewModel? favModel;

  final bool needUpdatePopularHeight;

  final BonusPoolEntity? bonusPool; // 奖池

  const GameHomeState({
    this.currentTabIndex = 0,
    this.currentPopularIndex = 0,
    this.bannerList = const [],
    this.marqueeList = const [],
    this.gameTypeList = const [],
    this.sectionHeights = const [],
    this.activities = const [],
    this.netState = NetState.idle,
    this.popularModel,
    this.recentlyModel,
    this.favModel,
    this.needUpdatePopularHeight = false,
    this.bonusPool,
  });

  GameHomeState copyWith(
      {int? currentTabIndex,
      int? currentPopularIndex,
      List<HomeFeedItem>? marqueeList,
      List<HomeBannerEntity>? bannerList,
      List<GameTypeV2>? gameTypeList,
      List<double>? sectionHeights,
      List<ActivityItem>? activities,
      NetState? netState,
      PopularSectionViewModel? popularModel,
      PopularSectionViewModel? recentlyModel,
      PopularSectionViewModel? favModel,
      bool? needUpdatePopularHeight,
      BonusPoolEntity? bonusPool}) {
    return GameHomeState(
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      currentPopularIndex: currentPopularIndex ?? this.currentPopularIndex,
      marqueeList: marqueeList ?? this.marqueeList,
      bannerList: bannerList ?? this.bannerList,
      gameTypeList: gameTypeList ?? this.gameTypeList,
      sectionHeights: sectionHeights ?? this.sectionHeights,
      activities: activities ?? this.activities,
      netState: netState ?? this.netState,
      popularModel: popularModel?.clone() ?? this.popularModel,
      recentlyModel: recentlyModel?.clone() ?? this.recentlyModel,
      favModel: favModel?.clone() ?? this.favModel,
      needUpdatePopularHeight: needUpdatePopularHeight ?? this.needUpdatePopularHeight,
      bonusPool: bonusPool ?? this.bonusPool,
    );
  }

  @override
  List<Object?> get props => [
    marqueeList,
        bannerList,
        gameTypeList,
        sectionHeights,
        activities,
        netState,
        currentTabIndex,
        currentPopularIndex,
        popularModel,
        recentlyModel,
        favModel,
        needUpdatePopularHeight,
        bonusPool,
      ];
}
