import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/activity.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/dialog/check_in_success_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/home/<USER>/home_expandable_activity_card.dart';

part 'activity_list_state.dart';

class ActivityListCubit extends Cubit<ActivityListState> {
  late StreamSubscription<bool> _isLoginSubscription;

  ActivityListCubit() : super(const ActivityListState());

  refreshData() {
    fetchGameActivityCategoryList();
    fetchTaskCategoryList();
    fetchCheckInData();
  }

  reset() {
    emit(const ActivityListState());
    refreshData();
  }

  @override
  Future<void> close() {
    _isLoginSubscription.cancel();
    return super.close();
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    emit(state.copyWith(isNoMoreDataState: isNoMoreDataState));
  }

  void onChangeTabIndex(int index) {
    emit(state.copyWith(currentTabIndex: index));
    refreshCurrentTabData();
  }

  void onChangeGameTabIndex(int index) {
    emit(state.copyWith(currentGameTabIndex: index));
  }

  void onChangeTaskTabIndex(int index) {
    emit(state.copyWith(currentTaskTabIndex: index));
  }

  /// 页面切换时，刷新当前页数据
  void refreshCurrentTabData() {
    if (state.currentTabIndex == 0) {
      if (state.gameCategoryList.isEmpty) {
        fetchGameActivityCategoryList();
      } else {
        fetchGameListDataByIndex(state.currentGameTabIndex);
      }
    } else if (state.currentTabIndex == 1) {
      if (state.taskCategoryList.isEmpty) {
        fetchTaskCategoryList();
      } else {
        fetchTaskListDataByIndex(state.currentTaskTabIndex);
      }
    }
  }

  Future<void> fetchCheckInData() async {
    try {
      final res = await UserApi.fetchCheckInList();
      // 计算签到统计
      int totalSignedDays = 0;
      double totalGoldEarned = 0;
      if (res != null) {
        for (final entity in res.fullList) {
          if (entity.signInState == SignInState.signed.value) {
            totalSignedDays += 1;
            totalGoldEarned += entity.signInAward;
          } else if (entity.signInState == SignInState.backdated.value) {
            totalSignedDays += 1;
            totalGoldEarned += entity.reSignInAward;
          }
        }
      }
      emit(state.copyWith(
        checkInModel: res,
        totalSignedDays: totalSignedDays,
        totalGoldEarned: totalGoldEarned,
      ));
    } catch (e) {
      LogD('Error fetching daily check in data: $e');
    } finally {
      emit(state.copyWith(
        checkInListNetState: state.checkInModel == null ? NetState.showReload : NetState.success,
      ));
    }
  }

  void calculateCheckInDaysTotal() {
    if (state.checkInModel == null) return;
    int totalSignedDays = 0;
    double totalGoldEarned = 0;
    for (var entity in state.checkInModel!.fullList) {
      if (entity.signInState == SignInState.signed.value) {
        totalSignedDays += 1;
        totalGoldEarned += entity.signInAward;
      } else if (entity.signInState == SignInState.backdated.value) {
        totalSignedDays += 1;
        totalGoldEarned += entity.reSignInAward;
      }
    }
    emit(state.copyWith(
      totalSignedDays: totalSignedDays,
      totalGoldEarned: totalGoldEarned,
    ));
  }

  Future<void> confirmClearCheckInInfo(DailyCheckInItem model) async {
    if (model.isFetching) return;
    model.isFetching = true;
    emit(state.copyWith(checkInModel: state.checkInModel)); // 触发刷新
    try {
      await UserApi.clearUserCheckInInfo();
      await fetchCheckInData();
    } catch (e) {
      model.isFetching = false;
      emit(state.copyWith(checkInModel: state.checkInModel));
    }
  }

  Future<void> onClickCheckBtn(BuildContext context, DailyCheckInItem model) async {
    if (model.isFetching) return;
    model.isFetching = true;
    emit(state.copyWith(checkInModel: state.checkInModel));
    try {
      final res = await UserApi.executeCheckIn(
        isBackdate: model.signInState == SignInState.needBackdate.value,
        date: model.day,
      );
      sl<UserCubit>().fetchUserBalance();
      if (res.$1) {
        await fetchCheckInData();
        final award = model.signInState == SignInState.needBackdate.value ? model.reSignInAward : model.signInAward;
        CheckInSuccessDialog(money: award, days: state.totalSignedDays).show(context);
      } else {
        model.isFetching = false;
        emit(state.copyWith(checkInModel: state.checkInModel));
        CommonDialog.show(
          context: context,
          title: 'act_hint'.tr(),
          content: res.$2,
          showCancelBtn: false,
        );
      }
    } catch (e) {
      model.isFetching = false;
      emit(state.copyWith(checkInModel: state.checkInModel));
    }
  }

  /// *********************************************** 游戏活动相关
  Future<void> fetchGameActivityCategoryList() async {
    if (state.gameNetState == NetState.loading) return;
    emit(state.copyWith(gameNetState: NetState.loading));
    final result = await ActivityApi.fetchActivityCategoryList(
      1,
      mapper: ActivityGameTypeViewModel.fromActivityCategory,
    );

    emit(state.copyWith(
      gameCategoryList: result,
      gameNetState: result.isEmpty ? NetState.showReload : NetState.success,
    ));

    if (result.isNotEmpty) {
      await fetchGameListDataByIndex(0);
      unawaited(_preloadOtherCategories(result.skip(1).toList()));
    }
  }

  Future<void> _preloadOtherCategories(List<ActivityGameTypeViewModel> categories) async {
    try {
      for (int i = 1; i < state.gameCategoryList.length; i++) {
        await fetchGameListDataByIndex(i);
      }
    } catch (e) {
      print('Preload error: $e');
    }
  }

  Future<void> fetchGameListDataByIndex(int index, {bool isLoadMore = false}) async {
    final list = state.gameCategoryList;
    if (index < 0 || index >= list.length) return;

    final vm = list[index];

    // 置为 loading
    final loadingVM = vm.copyWith(
      netState: NetState.loading,
      pageNo: isLoadMore ? vm.pageNo + 1 : 1,
    );
    final listLoading = List<ActivityGameTypeViewModel>.from(list)..[index] = loadingVM;
    emit(state.copyWith(gameCategoryList: listLoading));

    // 请求
    final result = await ActivityApi.fetchActivityList(
      pageNo: loadingVM.pageNo,
      activeCategory: loadingVM.category,
    );

    // 合并
    final merged =
        loadingVM.pageNo == 1 ? result.records : (List<ActivityRecords>.from(loadingVM.list)..addAll(result.records));
    final noMore = result.total <= merged.length;

    final updated = loadingVM.copyWith(
      list: merged,
      pageNo: noMore ? loadingVM.pageNo : loadingVM.pageNo + 1,
      isNoMoreDataState: noMore,
      netState: merged.isEmpty ? NetState.empty : NetState.success,
    );

    final listDone = List<ActivityGameTypeViewModel>.from(state.gameCategoryList)..[index] = updated;
    emit(state.copyWith(gameCategoryList: listDone));
  }

  /// *********************************************** 自助领取相关

  Future<void> fetchTaskCategoryList() async {
    final result = await ActivityApi.fetchActivityCategoryList(
      2,
      mapper: ActivityTaskTypeViewModel.fromActivityCategory,
    );

    emit(state.copyWith(
      taskCategoryList: result,
      taskNetState: result.isEmpty ? NetState.showReload : NetState.success,
    ));

    if (result.isNotEmpty && sl<UserCubit>().state.isLogin) {
      await fetchTaskListDataByIndex(0);
      emit(state.copyWith(taskNetState: NetState.success));
      unawaited(_preloadOtherTaskCategories(result.skip(1).toList()));
    }
  }

  Future<void> _preloadOtherTaskCategories(List<ActivityTaskTypeViewModel> categories) async {
    try {
      for (int i = 1; i < state.taskCategoryList.length; i++) {
        await fetchTaskListDataByIndex(i);
      }
    } catch (e) {
      print('task Preload error: $e');
    }
  }

  Future<List<ActivityTask>> fetchTaskListDataByIndex(int index) async {
    final list = state.taskCategoryList;
    if (index < 0 || index >= list.length) return [];

    final viewModel = list[index];

    // 置为 loading
    final loadingVM = viewModel.copyWith(netState: NetState.loading);
    final listLoading = List<ActivityTaskTypeViewModel>.from(list)..[index] = loadingVM;
    emit(state.copyWith(taskCategoryList: listLoading));

    final result = await ActivityApi.fetchTaskList(activeCategory: viewModel.category);

    final inProgressTask = result.firstWhereOrNull((task) => task.receiveStatus == 1);
    final processedTasks = <ActivityTask>[
      if (viewModel.category != 1)
        if (inProgressTask != null) ...[inProgressTask.clone()..isProcess = true],
      ...result,
    ];

    final updated = viewModel.copyWith(
      list: processedTasks,
      netState: result.isNotEmpty ? NetState.success : NetState.empty,
    );

    final listDone = List<ActivityTaskTypeViewModel>.from(state.taskCategoryList)..[index] = updated;
    emit(state.copyWith(taskCategoryList: listDone));
    return result;
  }

  bool isRequestCompleteTask = false;

  onClickCompleteTask({required int id, required int category}) async {
    if (isRequestCompleteTask) return;
    isRequestCompleteTask = true;
    GSEasyLoading.showLoading();
    try {
      final flag = await ActivityApi.completeActivityTask(id: id);
      if (flag) {
        final index = state.taskCategoryList.indexWhere((e) => e.category == category);
        if (index >= 0) fetchTaskListDataByIndex(index);
      }
    } finally {
      isRequestCompleteTask = false;
      GSEasyLoading.dismiss();
    }
  }
}
