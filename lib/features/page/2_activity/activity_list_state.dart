part of 'activity_list_cubit.dart';


class ActivityListState extends BaseState with EquatableMixin {
  /// 游戏活动、自助领取的 tab
  final int currentTabIndex;

  /// 今天星期几
  final int currentDayId;

  /// 今天是否已签到
  final bool isCheckedToday;

  /// 签到天数
  final int totalSignedDays;

  /// 累计奖励
  final double totalGoldEarned;

  /// 连续签到天数
  final int consecutiveSignedDays;

  final DailyCheckInEntity? checkInModel;
  final NetState checkInListNetState;

  final List<ActivityGameTypeViewModel> gameCategoryList;
  final NetState gameNetState;
  final int currentGameTabIndex;

  final List<ActivityTaskTypeViewModel> taskCategoryList;
  final NetState taskNetState;
  final int currentTaskTabIndex;

  const ActivityListState({
    // 仅保留需要的 BaseState 字段
    super.isNoMoreDataState = false,

    // 自身字段（等价于你原来的 init 默认值）
    this.currentTabIndex = 0,
    this.currentDayId = 1,
    this.isCheckedToday = false,
    this.totalSignedDays = 1,
    this.totalGoldEarned = 1,
    this.consecutiveSignedDays = 1,
    this.checkInModel,
    this.checkInListNetState = NetState.loading,
    this.gameCategoryList = const [],
    this.gameNetState = NetState.idle,
    this.currentGameTabIndex = 0,
    this.taskCategoryList = const [],
    this.taskNetState = NetState.loading,
    this.currentTaskTabIndex = 0,
  });

  ActivityListState copyWith({
    // BaseState
    bool? isNoMoreDataState,

    // 自身
    int? currentTabIndex,
    int? currentDayId,
    bool? isCheckedToday,
    int? totalSignedDays,
    double? totalGoldEarned,
    int? consecutiveSignedDays,
    DailyCheckInEntity? checkInModel,
    NetState? checkInListNetState,
    List<ActivityGameTypeViewModel>? gameCategoryList,
    NetState? gameNetState,
    int? currentGameTabIndex,
    List<ActivityTaskTypeViewModel>? taskCategoryList,
    NetState? taskNetState,
    int? currentTaskTabIndex,
  }) {
    return ActivityListState(
      // BaseState
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      // 自身
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      currentDayId: currentDayId ?? this.currentDayId,
      isCheckedToday: isCheckedToday ?? this.isCheckedToday,
      totalSignedDays: totalSignedDays ?? this.totalSignedDays,
      totalGoldEarned: totalGoldEarned ?? this.totalGoldEarned,
      consecutiveSignedDays: consecutiveSignedDays ?? this.consecutiveSignedDays,
      checkInModel: checkInModel ?? this.checkInModel,
      checkInListNetState: checkInListNetState ?? this.checkInListNetState,
      gameCategoryList: gameCategoryList ?? this.gameCategoryList,
      gameNetState: gameNetState ?? this.gameNetState,
      currentGameTabIndex: currentGameTabIndex ?? this.currentGameTabIndex,
      taskCategoryList: taskCategoryList ?? this.taskCategoryList,
      taskNetState: taskNetState ?? this.taskNetState,
      currentTaskTabIndex: currentTaskTabIndex ?? this.currentTaskTabIndex,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    isNoMoreDataState,
    // 自身
    currentTabIndex,
    currentDayId,
    isCheckedToday,
    totalSignedDays,
    totalGoldEarned,
    consecutiveSignedDays,
    checkInModel,
    checkInListNetState,
    gameCategoryList,
    gameNetState,
    currentGameTabIndex,
    taskCategoryList,
    taskNetState,
    currentTaskTabIndex,
  ];
}
