import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/activity/activity_list_cell.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/shared/widgets/platform/platform_tab_cell.dart';
import 'package:easy_localization/easy_localization.dart';

class ActivityGameListPage extends StatefulWidget {
  final List<ActivityGameTypeViewModel> dataList;
  final NetState netState;
  final int index;
  final GestureTapCallback refreshMethod;
  final Future Function(int index, bool isLoadMore) onFetch;
  final Function(int) onChangeTabIndex;
  final Function(ActivityRecords) onClickCell;

  const ActivityGameListPage({
    super.key,
    required this.dataList,
    required this.netState,
    required this.index,
    required this.refreshMethod,
    required this.onFetch,
    required this.onChangeTabIndex,
    required this.onClickCell,
  });

  @override
  State<StatefulWidget> createState() => _ActivityGameListPageState();
}

class _ActivityGameListPageState extends State<ActivityGameListPage> with TickerProviderStateMixin {
  final paddingH = 20.gw;

  late AnimationController _scaleController;
  static const Duration _scaleDuration = Duration(milliseconds: 200);
  int? _animatingIndex;
  late AnimationController _slideController;
  PageController pageController = PageController();
  final Map<String, RefreshController> _refreshControllers = {};

  @override
  void initState() {
    _scaleController = AnimationController(
      vsync: this,
      duration: _scaleDuration,
    );
    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    // 初始化时直接设置动画值为1.0，但是后续的tab切换会从0开始动画
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideController.value = 1.0;
      if (pageController.hasClients) {
        pageController.jumpToPage(widget.index);
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    pageController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// 下拉刷新
  Future _onRefresh(int index) {
    return widget.onFetch(index, false);
  }

  /// 上拉加载更多
  Future _onLoading(int index) {
    return widget.onFetch(index, true);
  }

  Widget _buildRightListView({
    required int index,
    required ActivityGameTypeViewModel model,
    required RefreshController refreshController,
  }) {
    LogV("model.refreshStatus>> ${model.netState}, model.list.length>> ${model.list.length}");
    if (model.netState == NetState.showReload) {
      return NetErrorWidget(
        refreshMethod: () => _onRefresh(index),
      );
    }

    if (model.netState == NetState.empty) {
      return EmptyWidget(title: 'act_no_active_events'.tr());
    }

    if (model.netState == NetState.loading && model.list.isEmpty) {
      return Container(
        alignment: Alignment.center,
        child: SizedBox(
          width: 20.gw,
          height: 20.gw,
          child: CircularProgressIndicator(strokeWidth: 2.gw),
        ),
      );
    }
    final body = AnimationLimiter(
      child: CommonRefresher(
        key: ValueKey('activity_page_${model.categoryName}'),
        enablePullDown: true,
        enablePullUp: true,
        refreshController: refreshController,
        onRefresh: () async {
          await _onRefresh(index);
          refreshController.refreshCompleted();
          refreshController.resetNoData();
        },
        onLoading: () async {
          bool hasNoMoreData = model.isNoMoreDataState ?? false;

          if (!hasNoMoreData) {
            await _onLoading(index);
            refreshController.loadComplete();
          } else {
            refreshController.loadNoData();
          }
        },
        listWidget: ListView.builder(
          padding: EdgeInsets.only(right: paddingH, top: 5.gw, bottom: 10.gw),
          itemCount: model.list.length,
          itemBuilder: (BuildContext context, int index) {
            final record = model.list[index];
            return AnimationConfiguration.staggeredList(
              position: model.pageNo == 1 ? index : 0,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: ActivityListCell(
                    model: record,
                    onTap: () => widget.onClickCell(record),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 0.15),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOut,
      )),
      child: body,
    );
  }

  Widget _buildLeftTabList() {
    return SizedBox(
      width: 60.gw + paddingH + 16.gw,
      child: ListView.separated(
        padding: EdgeInsets.only(left: paddingH, right: 16.gw, top: 5.gw, bottom: 10.gw),
        itemBuilder: (context, index) => _buildTabItem(index),
        separatorBuilder: (_, __) => SizedBox(height: 16.gw),
        itemCount: widget.dataList.length,
      ),
    );
  }

  Widget _buildTabItem(int index) {
    final model = widget.dataList[index];
    final isSelected = widget.index == index;
    Widget cell = PlatformTabCell(
      model: PlatformTabCellViewModel.fromActivityGameTypeViewModel(model),
      isSel: isSelected,
    );

    if (_animatingIndex == index) {
      cell = _wrapWithScaleAnimation(cell);
    }

    return InkWell(
      onTap: () => _handleTabTap(index, model),
      child: cell,
    );
  }

  Widget _buildRightPageView() {
    return Expanded(
      child: PageView.builder(
        controller: pageController,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.dataList.length,
        itemBuilder: (_, index) {
          final model = widget.dataList[index];
          final refreshController = _refreshControllers.putIfAbsent(
            model.categoryName,
            () => RefreshController(initialRefresh: false),
          );
          return _buildRightListView(index: index, model: model, refreshController: refreshController);
        },
      ),
    );
  }

  void _handleTabTap(int index, ActivityGameTypeViewModel model) {
    if (widget.index == index) return;

    setState(() {
      _animatingIndex = index;
    });
    widget.onChangeTabIndex(index);
    widget.onFetch(index, false);

    pageController.jumpToPage(index);
    _slideController.reset();
    _slideController.forward();

    _scaleController.forward().then((_) {
      _scaleController.reverse().then((_) {
        setState(() => _animatingIndex = null);
      });
    });
  }

  Widget _wrapWithScaleAnimation(Widget child) {
    return ScaleTransition(
      scale: Tween<double>(begin: 1.0, end: 1.1).animate(
        CurvedAnimation(
          parent: _scaleController,
          curve: Curves.easeInOut,
        ),
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.dataList.isEmpty) {
      if (widget.netState == NetState.showReload) {
        return NetErrorWidget(refreshMethod: widget.refreshMethod);
      }

      return Center(
        child: SizedBox(
          width: 25.gw,
          height: 25.gw,
          child: const CircularProgressIndicator(),
        ),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLeftTabList(),
        _buildRightPageView(),
      ],
    );
  }
}
