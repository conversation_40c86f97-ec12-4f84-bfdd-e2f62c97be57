import 'package:equatable/equatable.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';

import '../../../../core/base/base_state.dart';

class ActivityDetailState extends Equatable {
  final ActivityRecords? model;
  final NetState fetchModelState;
  final ActivityRewardStatus collectStatus;
  final NetState fetchCollectStatus;
  final NetState bindStatus;
  final bool isVerified;
  final String phoneNo;

  const ActivityDetailState({
    this.model,
    this.fetchModelState = NetState.idle,
    this.collectStatus = ActivityRewardStatus.unknown,
    this.fetchCollectStatus = NetState.idle,
    this.bindStatus = NetState.idle,
    this.isVerified = false,
    this.phoneNo = '',
  });

  ActivityDetailState copyWith({
    ActivityRecords? model,
    NetState? fetchModelState,
    ActivityRewardStatus? collectStatus,
    NetState? fetchCollectStatus,
    NetState? bindStatus,
    bool? isVerified,
    String? phoneNo,
  }) {
    return ActivityDetailState(
      model: model ?? this.model,
      fetchModelState: fetchModelState ?? this.fetchModelState,
      collectStatus: collectStatus ?? this.collectStatus,
      fetchCollectStatus: fetchCollectStatus ?? this.fetchCollectStatus,
      bindStatus: bindStatus ?? this.bindStatus,
      isVerified: isVerified ?? this.isVerified,
      phoneNo: phoneNo ?? this.phoneNo,
    );
  }

  @override
  List<Object?> get props => [
        model,
        fetchModelState,
        collectStatus,
        fetchCollectStatus,
        bindStatus,
        isVerified,
        phoneNo,
      ];
}
