import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/models/entities/withdraw_user_bank_list_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/3_chat_new/screens/chat_screen.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/sheet/fund_pwd_sheet.dart';

import '../transact_cubit.dart';
import 'withdraw_state.dart';

class WithdrawCubit extends Cubit<WithdrawState> {
  WithdrawCubit() : super(const WithdrawState()) {
    fetchUserBankList();
    fetchUserBalance();
  }

// 取消标记
  int _fetchSequence = 0;

// 输入金额
  void updateInput(String input) {
    emit(state.copyWith(input: input));
  }

// 获取人工提现通道
  Future<void> fetchManualChannelList() async {
    final list = await TransactApi.fetchCashOutManualChannelList();

    // 选择规则：优先用当前选中的 channelId，否则取第一个
    final selected = (list != null && list.isNotEmpty)
        ? list.firstWhere(
            (e) => e.channelId == state.currentSelManualChannel?.channelId,
            orElse: () => list.first,
          )
        : null;

    // needShowManualUSDTWithdrawWidget()==false 时要清除 fetchingBankInfo
    final needUsdtWidget = GlobalConfig.needShowManualUSDTWithdrawWidget();

    emit(state.copyWith(
      manualChannelList: list ?? const [],
      currentSelManualChannel: selected,
      fetchingBankInfo: needUsdtWidget ? state.fetchingBankInfo : false,
    ));
  }

// 切换人工通道
  Future<void> onChangeCurrentManualChannel(WithdrawManualChannelEntity model) async {
    if (state.currentSelManualChannel == model) return;
    emit(state.copyWith(currentSelManualChannel: model));
    await fetchUserBankInfo(); // 保持行为一致
  }

// 提现类型参数
  (int aType, int wldType) _getWithdrawTypeParams(WithdrawType type) {
    switch (type) {
      case WithdrawType.bankCard:
        return (1, 0);
      case WithdrawType.wallet:
        return (2, 0);
      case WithdrawType.manualChannel:
        return (2, 1);
    }
  }

// 切换提现类型
  Future<void> onChangeWithdrawType(WithdrawType type) async {
    if (state.type == type) return;

    _fetchSequence++;
    final currentSeq = _fetchSequence;

    // 先置 loading 与基础清空（选中项）
    emit(state.copyWith(
      type: type,
      fetchingBankInfo: true,
      currentSelBank: null,
      currentSelWallet: null,
      currentSelUsdt: null,
    ));

    if (type == WithdrawType.manualChannel) {
      if (GlobalConfig.needShowManualUSDTWithdrawWidget()) {
        await fetchManualChannelList();
      } else {
        await fetchUserBankInfo();
        await fetchManualChannelList();
      }
    }

    final (aType, wldType) = _getWithdrawTypeParams(type);

    try {
      final list = await TransactApi.fetchCashOutUserBankList(aType, wldType: wldType);
      if (currentSeq != _fetchSequence) return; // 过期请求丢弃

      if (list == null) {
        emit(state.copyWith(netState: NetState.showReload));
        return;
      }

      // 写入列表
      switch (type) {
        case WithdrawType.bankCard:
          emit(state.copyWith(myBankCardList: list, netState: NetState.success));
          break;
        case WithdrawType.wallet:
          emit(state.copyWith(myWalletList: list, netState: NetState.success));
          break;
        case WithdrawType.manualChannel:
          emit(state.copyWith(myUsdtList: list, netState: NetState.success));
          break;
      }

      // 自动选择一个可用项并拉详情
      if (list.isNotEmpty) {
        final model = list.firstWhere((e) => e.useStatus != 1, orElse: () => list.first);
        await fetchUserBankInfo(userBankInfoId: model.id, sequence: currentSeq);
      }
    } finally {
      if (currentSeq == _fetchSequence) {
        emit(state.copyWith(fetchingBankInfo: false));
      }
    }
  }

// 获取用户可提现状态
  Future<WithdrawStatusEntity> fetchCashOutAvailable() async {
    return await TransactApi.fetchCashOutAvailable();
  }

// 拉取某类账户列表（银行卡/钱包/手动通道）
  Future<void> fetchUserBankList({bool showLoading = false, WithdrawType? type}) async {
    type ??= state.type;
    if (showLoading) GSEasyLoading.showLoading();

    if (type == WithdrawType.manualChannel) {
      if (GlobalConfig.needShowManualUSDTWithdrawWidget()) {
        await fetchManualChannelList();
      } else {
        await fetchUserBankInfo();
        await fetchManualChannelList();
      }
      if (showLoading) GSEasyLoading.dismiss();
      return;
    }

    final (aType, wldType) = _getWithdrawTypeParams(type);
    final list = await TransactApi.fetchCashOutUserBankList(aType, wldType: wldType);

    if (list == null) {
      emit(state.copyWith(netState: NetState.showReload));
      if (showLoading) GSEasyLoading.dismiss();
      return;
    }

    switch (type) {
      case WithdrawType.bankCard:
        emit(state.copyWith(myBankCardList: list, netState: NetState.success));
        break;
      case WithdrawType.wallet:
        emit(state.copyWith(myWalletList: list, netState: NetState.success));
        break;
      case WithdrawType.manualChannel:
        emit(state.copyWith(myUsdtList: list, netState: NetState.success));
        break;
    }

    if (list.isNotEmpty) {
      final model = list.firstWhere((e) => e.useStatus != 1, orElse: () => list.first);
      await fetchUserBankInfo(userBankInfoId: model.id);
    } else {
      emit(state.copyWith()); // 触发一帧（可省略）
    }

    if (showLoading) GSEasyLoading.dismiss();
  }

// 余额（交给 UserCubit）
  Future<void> fetchUserBalance() async {
    await sl<UserCubit>().fetchUserBalance();
  }

// 拉取已选账户详情
  Future<void> fetchUserBankInfo({
    int? userBankInfoId,
    bool showLoading = true,
    int? sequence,
  }) async {
    if (sequence != null && sequence != _fetchSequence) return;

    if (showLoading) emit(state.copyWith(fetchingBankInfo: true));

    final model = await TransactApi.fetchCashOutUserBankInfo(userBankInfoId: userBankInfoId);

    if (sequence != null && sequence != _fetchSequence) return;

    if (showLoading) {
      // 不改变其它字段，只落 fetchingBankInfo
      emit(state.copyWith(fetchingBankInfo: false));
    }

    if (model != null) {
      // 根据当前类型写入选中模型与可提现余额
      switch (state.type) {
        case WithdrawType.bankCard:
          emit(state.copyWith(
            currentSelBank: model,
            availableBalance: model.withdrawalAmount,
          ));
          break;
        case WithdrawType.wallet:
          emit(state.copyWith(
            currentSelWallet: model,
            availableBalance: model.withdrawalAmount,
          ));
          break;
        case WithdrawType.manualChannel:
          if (GlobalConfig.needShowManualUSDTWithdrawWidget()) {
            emit(state.copyWith(
              currentSelUsdt: model,
              availableBalance: model.withdrawalAmount,
            ));
          } else {
            emit(state.copyWith(availableBalance: model.withdrawalAmount));
          }
          break;
      }
    }
  }

// 提现（银行卡/钱包）
  Future<void> submitBankWithdraw(
    BuildContext context, {
    required TextEditingController controller,
  }) async {
    if (state.input.isEmpty) {
      GSEasyLoading.showToast("please_enter_withdraw_amount".tr());
      return;
    }

    if (state.type == WithdrawType.manualChannel) {
      await submitManualChannelWithdraw(context, controller: controller);
      return;
    }

    if (await judgeWithdrawBindPhoneLimit(context) == false) return;

    final WithdrawUserBankInfoEntity? model =
        (state.type == WithdrawType.bankCard) ? state.currentSelBank : state.currentSelWallet;

    if (model == null) {
      GSEasyLoading.showToast(
          "${"please_select".tr()}${state.type == WithdrawType.bankCard ? "bank_card".tr() : "wallet".tr()}");
      return;
    }
    if (model.useStatus == 1) {
      CommonDialog.show(context: context, content: "withdraw_method_disabled_contact_support".tr());
      return;
    }

    final amount = double.tryParse(state.input) ?? 0;
    if (amount < model.amountMinLimit) {
      emit(state.copyWith(fetchingSubmit: false));
      GSEasyLoading.showToast(
          'withdraw_amount_less_than_zero'.tr().replaceAll('{min}', model.amountMinLimit.removeZeros));
      return;
    }

    emit(state.copyWith(fetchingSubmit: true));

    final isAvailable = await _checkWithdrawAvailability(context);
    if (!isAvailable) {
      emit(state.copyWith(fetchingSubmit: false));
      return;
    }

    if (sl<UserCubit>().isFundPasswordInputLocked) {
      emit(state.copyWith(fetchingSubmit: false));
      GSEasyLoading.showToast("pay_pwd_error_limit".tr());
      return;
    }

    final fundPwd = await FundPwdSheet(context).show();
    if (fundPwd == null) {
      emit(state.copyWith(fetchingSubmit: false));
      GSEasyLoading.showToast("enter_fund_password".tr());
      return;
    }

    final ok = await TransactApi.submitCashOutByBank(
      amount: amount,
      fundPwd: fundPwd,
      userBankInfoId: model.userBankInfoId,
    );

    // 无论成功与否，都更新选中模型信息与提交态
    emit(state.copyWith(fetchingSubmit: false));
    updateCurrentSelModelInfo();

    if (ok) {
      sl<UserCubit>().resetFundPasswordErrorLock();
      sl<UserCubit>().fetchWithdrawOrderList();
      controller.text = "";
      CommonDialog.show(
        context: context,
        title: 'act_hint'.tr(),
        content: "withdraw_request_submitted".tr(),
        showCancelBtn: false,
      );
    }
  }

// 可用性检查
  Future<bool> _checkWithdrawAvailability(BuildContext context) async {
    final withdrawStatus = await fetchCashOutAvailable();
    if (withdrawStatus.status == null) return false;

    if (withdrawStatus.status == false) {
      final needCS = withdrawStatus.statusCode == 200;
      CommonDialog.show(
        context: context,
        title: 'act_hint'.tr(),
        content: withdrawStatus.errorMessage,
        sureBtnTitle: needCS ? "contact_support".tr() : "go_to_recharge".tr(),
        showCancelBtn: needCS,
        complete: () => needCS ? SystemUtil.contactService() : sl<TransactCubit>().jumpToPage(0),
      );
      return false;
    }
    return true;
  }

// 人工通道提现
  Future<void> submitManualChannelWithdraw(
    BuildContext context, {
    required TextEditingController controller,
  }) async {
    final ch = state.currentSelManualChannel;
    if (ch == null) {
      GSEasyLoading.showToast("please_select_channel_first".tr());
      return;
    }

    final amount = double.tryParse(state.input) ?? 0;
    if (amount <= 0) {
      GSEasyLoading.showToast("withdraw_amount_must_gt_zero".tr());
      return;
    }

    if (await judgeWithdrawBindPhoneLimit(context) == false) return;

    final isAvailable = await _checkWithdrawAvailability(context);
    if (!isAvailable) return;

    WithdrawUserBankInfoEntity? model;
    String? fundPwd;

    if (GlobalConfig.needShowManualUSDTWithdrawWidget()) {
      model = state.currentSelUsdt;
      if (model == null) {
        GSEasyLoading.showToast("please_select_withdraw_address".tr());
        return;
      }
      if (model.useStatus == 1) {
        CommonDialog.show(context: context, content: "withdraw_method_disabled_contact_support".tr());
        return;
      }
      if (amount < model.amountMinLimit) {
        emit(state.copyWith(fetchingSubmit: false));
        GSEasyLoading.showToast(
          'withdraw_amount_less_than_zero'.tr().replaceAll('{min}', model.amountMinLimit.removeZeros));
        return;
      }
      if (sl<UserCubit>().isFundPasswordInputLocked) {
        emit(state.copyWith(fetchingSubmit: false));
        GSEasyLoading.showToast("pay_pwd_error_limit".tr());
        return;
      }
      fundPwd = await FundPwdSheet(context).show();
      if (fundPwd == null) {
        emit(state.copyWith(fetchingSubmit: false));
        GSEasyLoading.showToast("enter_fund_password".tr());
        return;
      }
    }

    emit(state.copyWith(fetchingSubmit: true));

    GSEasyLoading.showLoading();
    final res = await TransactApi.submitOfflineOrder(
      channelId: ch.channelId,
      amount: amount,
      fundPwd: fundPwd,
      userBankInfoId: model?.userBankInfoId,
      isTopUp: false,
    );
    GSEasyLoading.dismiss();

    updateCurrentSelModelInfo();
    emit(state.copyWith(fetchingSubmit: false));

    if (res != null) {
      sl<UserCubit>().fetchWithdrawOrderList();
      sl<UserCubit>().resetFundPasswordErrorLock();
      sl<MainScreenCubit>().onChangeShowChatFloatWidget(false);
      await Navigator.push<dynamic>(
        context,
        MaterialPageRoute(
          builder: (context) => ChatScreen(
            selectedConversation: getConversation(
              userID: res.userNo,
              name: res.nickName,
            ),
          ),
        ),
      );
      sl<MainScreenCubit>().onChangeShowChatFloatWidget(true);
      controller.text = "";
    }
  }

// 绑定手机判断
  Future<bool> judgeWithdrawBindPhoneLimit(BuildContext context) async {
    final needCheckPhone = await GlobalConfig().getConfigValueByKey("wtdCheckPhone");
    if (needCheckPhone == '2') {
      if (sl<UserCubit>().state.userInfo!.phoneNo.isEmpty) {
        CommonDialog.show(
          context: context,
          title: 'act_hint'.tr(),
          content: "bind_phone_before_withdraw".tr(),
          complete: () {
            sl<NavigatorService>().push(
              AppRouter.updateProfile,
              arguments: {'initialValue': '', 'field': UserFieldType.phone},
            );
          },
        );
        return false;
      }
    }
    return true;
  }

// 更新可提现余额（保持原有行为）
  void updateCurrentSelModelInfo() {
    switch (state.type) {
      case WithdrawType.bankCard:
      case WithdrawType.wallet:
        final model = state.type == WithdrawType.bankCard ? state.currentSelBank : state.currentSelWallet;
        if (model != null) {
          fetchUserBankInfo(userBankInfoId: model.userBankInfoId);
        }
        break;
      case WithdrawType.manualChannel:
        final model = state.currentSelUsdt;
        if (model != null && GlobalConfig.needShowManualUSDTWithdrawWidget()) {
          fetchUserBankInfo(userBankInfoId: model.userBankInfoId);
        } else {
          fetchUserBankInfo();
        }
        break;
    }
  }

  resetData() {
    emit(const WithdrawState());
  }
}
