import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/common_app_bar.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/shared/widgets/row/text_row.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/transact/withdraw/withdraw_history_cell.dart';

class WithdrawHistoryDetailPage extends StatelessWidget {
  final WithdrawRecord model;

  const WithdrawHistoryDetailPage({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(pageTitle: "withdraw_detail".tr()), // 提现详情
      body: CommonCardPage(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
            margin: EdgeInsets.fromLTRB(15.gw, 10.gw, 15.gw, 0),
            decoration:
                BoxDecoration(color: context.theme.cardColor, borderRadius: BorderRadius.circular(4.gw), boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 0),
              ),
            ]),
            child: Column(
              children: [
                TextRow(
                  title: "current_status".tr(),
                  rightWidget: WithdrawHistoryCell.buildOrderStatusWidget(context, orderStatus: model.orderStatus),
                ),
                TextRow(
                  title: "deposit_order_no".tr(),
                  rightWidget: GSTextImageButton(
                    text: model.transactionNo,
                    textStyle: Theme.of(context).textTheme.titleMedium,
                    imageAssets: "assets/images/common/icon_copy_black.png",
                    position: GSTextImageButtonPosition.right,
                    onPressed: () {
                      ClipboardTool.setData(model.transactionNo);
                    },
                  ),
                ),
                TextRow(
                  title: "withdraw_methods".tr(),
                  content: model.cashoutWayName,
                ),
                TextRow(
                  title: "withdrawal_type".tr(),
                  content: model.cashoutTypeName,
                ),
                if (model.orderAmount > 0)
                  TextRow(
                    title: "order_amount".tr(),
                    content: "${model.orderAmount}",
                  ),
                if (model.reduceAmount > 0)
                  TextRow(
                    title: "discount_amount".tr(),
                    content: "${model.reduceAmount}",
                  ),
                TextRow(
                  title: "service_charge_rate".tr(),
                  content: "${model.serviceChargeRate}%",
                ),
                TextRow(
                  title: "service_charge".tr(),
                  content: "${model.serviceCharge}",
                ),
                if (model.finalAmount > 0)
                  TextRow(
                    title: "received_amount".tr(),
                    content: "${model.finalAmount}",
                  ),
                if (model.cardNo.isNotEmpty)
                  TextRow(
                    title: "transfer_bank_card_number".tr(),
                    content: model.cardNo,
                  ),
                TextRow(
                  title: "withdrawal_time".tr(),
                  content: TimeUtil.convertTimeStampToFull(model.requestTime),
                ),
                TextRow(
                  title: "operation_time".tr(),
                  content: TimeUtil.convertTimeStampToFull(model.operateTime),
                ),
                if (model.orderStatus == 2) // 未通过显示「拒绝原因」
                  TextRow(
                    title: "remarks".tr(),
                    content: model.refusalRemark,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
