import 'package:draggable_float_widget/draggable_float_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/dialog_queue_manager/dialog_queue_manager.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_home_state.dart';
import 'package:wd/features/page/1_game_home/game_home_cubit.dart';
import 'package:wd/features/page/4_mine/notifications/notification_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/main.dart';
import 'package:wd/shared/widgets/app_download/app_download_widget.dart';
import 'package:wd/shared/widgets/keep_alive_wrapper.dart';
import 'package:wd/shared/widgets/main/chat_badge.dart';
import 'package:wd/shared/widgets/tiktok/home/<USER>';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/shared/widgets/transact/withdraw/withdraw_status_floating_button.dart';

import 'main_screen_cubit.dart';
import 'main_screen_state.dart';

class MainScreenPage extends StatefulWidget {
  const MainScreenPage({super.key});

  @override
  State<StatefulWidget> createState() => _MainScreenPageState();
}

class _MainScreenPageState extends State<MainScreenPage> with RouteAware {
  final PageController _pageController = PageController();
  late final tabBarHeight = 49 + MediaQuery.of(context).padding.bottom;
  OverlayEntry? entryHolder;
  bool _showChatFloatWidget = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemUtil.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
      // 显示聊天浮标
      _showFloatWidget();
      // 获取个人消息通知
      sl<NotificationsCubit>().fetchNotifications(updateCount: true, reset: true);
      // 开始注册送礼金弹窗计时器
      sl<MainScreenCubit>().startSignupBonusTimer();
    });
  }

  @override
  dispose() {
    super.dispose();
    routeObserver.unsubscribe(this);
    _hideFloatWidget();
  }


  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 订阅路由观察者
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didPopNext() {
    // viewWillAppear(_:) / viewDidAppear(_:)
    // 返回到此路由
    DialogQueueManager().resumeQueue();
    final currentType = sl<MainScreenCubit>().state.currentTabType;

    switch (currentType) {
      case BottomNavType.gameHome:
        sl<GameHomeCubit>().fetchPopularAndFavList();
        break;
      default:
        break;
    }
  }

  @override
  void didPushNext() {
    // viewWillDisappear(_:) / viewDidDisappear(_:)
    // 此路由被覆盖
    DialogQueueManager().pauseQueue();
  }


  @override
  void didPop() {
    // viewDidDisappear
    // 页面被弹出时，暂停弹窗队列（因为离开了该界面）
    DialogQueueManager().pauseQueue();
  }

  @override
  void didPush() {
    // viewDidAppear
    // 页面被推入时，恢复弹窗队列（因为进入了该界面）
    DialogQueueManager().resumeQueue();
  }


  _hideFloatWidget() {
    entryHolder?.remove();
    entryHolder = null;
  }

  _showFloatWidget() {
    if (entryHolder != null) return;
    OverlayEntry overlayEntry = OverlayEntry(builder: (context) {
      return DraggableFloatWidget(
        config: DraggableFloatWidgetBaseConfig(
          isFullScreen: false,
          initPositionYInTop: false,
          initPositionXInLeft: false,
          initPositionYMarginBorder: SystemUtil.isWeb()
              ? (GlobalConfig().deviceRealHeight * 0.32)
              : (MediaQuery.of(context).size.height * 0.32),
          borderTop: MediaQuery.of(context).padding.top,
          borderRight: SystemUtil.isWeb() ? (GlobalConfig().deviceRealWidth - GSScreenUtil().screenWidth) : 0,
          animDuration: SystemUtil.isWeb() ? Duration.zero : const Duration(milliseconds: 300),
          borderBottom: 0,
        ),
        onTap: () {
          if (!sl<UserCubit>().state.isLogin) {
            AuthUtil.checkIfLogin(() async {
              await Future.delayed(const Duration(seconds: 1));
              sl<MainScreenCubit>().goToChatPage();
            });
          } else {
            sl<MainScreenCubit>().goToChatPage();
          }
        },
        child: Stack(
          alignment: Alignment.topRight,
          clipBehavior: Clip.none,
          children: [
            Image.asset(
              "assets/images/chat/ui/btn_chat_float.png",
              width: 54.gw,
              height: 54.gw,
            ),
            const ChatBadge()
          ],
        ),
      );
    });
    sl<NavigatorService>().navigatorKey.currentState?.overlay?.insert(overlayEntry);
    //保存这个引用，**用于清除这个组件**
    entryHolder = overlayEntry;
  }

  @override
  Widget build(BuildContext context) {
    // print("context.locale1>>>>> ${context.locale}");
    return BlocSelector<UserCubit, UserState, bool>(
      selector: (state) => state.userInfo?.tiktokTabVisible ?? true,
      builder: (context, tiktokTabVisible) {
        /// 处理当visibleConfigs 发送变化的时候，pageController.page没有及时更新
        final visibleConfigs = BottomNavConfig.getVisibleConfigs(context, tiktokTabVisible: tiktokTabVisible);
        final currentIndex =
            visibleConfigs.indexWhere((e) => e.type == context.read<MainScreenCubit>().state.currentTabType);
        final safeIndex = currentIndex == -1 ? 0 : currentIndex;

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients && _pageController.page?.round() != safeIndex) {
            _pageController.jumpToPage(safeIndex);
          }
        });
        return BlocListener<MainScreenCubit, MainScreenState>(
          listenWhen: (previous, current) =>
              current.currentTabType != previous.currentTabType ||
              current.showChatFloatWidget != previous.showChatFloatWidget,
          listener: (context, state) {
            final index = visibleConfigs.indexWhere((e) => e.type == state.currentTabType);
            if (_pageController.hasClients && _pageController.page?.round() != index) {
              _pageController.jumpToPage(index);
            }

            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_showChatFloatWidget != state.showChatFloatWidget) {
                _showChatFloatWidget = state.showChatFloatWidget;
                _showChatFloatWidget ? _showFloatWidget() : _hideFloatWidget();
              }
            });
          },
          child: PopScope(
            canPop: false,
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.bottomCenter,
              children: [
                Positioned.fill(
                  child: Column(
                    children: [
                      // /// 网页App下载控件
                      // BlocSelector<MainScreenCubit, MainScreenState, bool>(
                      //   selector: (state) => kIsWeb && state.showAppDownloadTips,
                      //   builder: (context, showAppDownloadTips) {
                      //     return AppDownloadWidget(
                      //       enable: showAppDownloadTips,
                      //       onClickClose: () => sl<MainScreenCubit>().onChangeWebsiteAppDownloadTips(false),
                      //     );
                      //   },
                      // ),

                      /// 子视图
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(bottom: tabBarHeight),
                          child: PageView(
                            controller: _pageController,
                            physics: const NeverScrollableScrollPhysics(),
                            children: visibleConfigs.map((config) => KeepAliveWrapper(child: config.page)).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                /// TabBar
                BlocSelector<MainScreenCubit, MainScreenState, BottomNavType>(
                  selector: (state) => state.currentTabType,
                  builder: (context, currentTabType) {
                    return CommonBottomNavigationBar(
                      data: visibleConfigs,
                      currentTabType: currentTabType,
                      onTabSwitch: (model) => sl<MainScreenCubit>().selectedNavTypeChanged(model.type),
                    );
                  },
                ),

                /// 提现浮动按钮
                BlocSelector<UserCubit, UserState, String?>(
                  selector: (state) => state.withdrawFloatBtnTitle,
                  builder: (context, title) {
                    if (StringUtil.isEmpty(title)) return const SizedBox.shrink();
                    return Positioned(
                      top: 290.gw,
                      right: 0,
                      child: BlocSelector<MainScreenCubit, MainScreenState, GlobalKey>(
                        selector: (state) => state.withdrawFloatButtonKey,
                        builder: (context, key) {
                          return WithdrawStatusFloatingButton(key: key, title: title!);
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
