import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/models/apis/notification.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_home_state.dart';
import 'package:wd/features/page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter_cubit.dart';
import 'package:wd/features/page/1_game_home/game_home_cubit.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/3_transact/transact_cubit.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/dialog/notification_alert_dialog.dart';
import 'package:wd/shared/widgets/dialog/signup_bonus_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'main_screen_state.dart';

class MainScreenCubit extends Cubit<MainScreenState> {
  MainScreenCubit() : super(MainScreenState().init());

  void onChangeWebsiteAppDownloadTips(bool isShow) {
    emit(state.copyWith(showAppDownloadTips: isShow));
  }

  void onChangeShowChatFloatWidget(bool flag) {
    emit(state.copyWith(showChatFloatWidget: flag));
  }

  void selectedNavTypeChanged(BottomNavType navType) {
    final current = state.currentTabType;
    final userCubit = sl<UserCubit>();
    final navigator = sl<NavigatorService>();

    // 已经是当前 tab，直接 return
    if (current == navType) return;

    // 未登录且点击的是 transact tab
    if (!userCubit.state.isLogin && navType == BottomNavType.agent) {
      navigator.push(AppRouter.login);
      return;
    }

    // 按 tab 类型执行逻辑
    switch (navType) {
      case BottomNavType.gameHome:
        _handleGameTab();
        break;
      case BottomNavType.videoHome:
        sl<PopularVideoFilterCubit>().fetchHotVideo();
        break;
      case BottomNavType.promotion:
        _handleActivityTab();
        break;
      case BottomNavType.agent:
        _handleAgentTab();
        break;
      case BottomNavType.mine:
        _handleMineTab();
        break;
        default:
          break;
    }

    // 更新状态
    emit(state.copyWith(currentTabType: navType));

    // 收起浮动按钮
    state.withdrawFloatButtonKey.currentState?.close();

  }


  void _handleGameTab() {
    sl<GameHomeCubit>().fetchData();
  }

  void _handleActivityTab() {
    sl<ActivityListCubit>().fetchCheckInData();
    sl<ActivityListCubit>().refreshCurrentTabData();
  }

  void _handleAgentTab() {

    sl<PromotionRewardsCubit>().fetchTeam();
    sl<PromotionRewardsCubit>().fetchMyTeam();
    sl<PromotionRewardsCubit>().fetchCommission();
  }

  void _handleMineTab() {
    GameUtil.transferOutFromLoginPlatform();
    final userCubit = sl<UserCubit>();
    userCubit.fetchUserInfo();
    userCubit.fetchUserVip();
    userCubit.fetchVideoVipInfo();
    if (userCubit.state.inviteInfo == null) userCubit.fetchInviteInfo();
  }

  /// 前往充值、提现页面
  goToTransactPage({required bool isDeposit}) {
    if (isDeposit) {
      sl<NavigatorService>().push(AppRouter.recharge);
    } else {
      sl<NavigatorService>().push(AppRouter.withdraw);
    }
  }

  goToChatPage() async {
    final cubit = sl<UserCubit>();
    final hasConfig = cubit.state.tencentConfig != null;

    if (!hasConfig) {
      GSEasyLoading.showLoading();
      final success = await cubit.fetchTencentConfig();
      GSEasyLoading.dismiss();

      if (!success) return; // 请求失败，中断
    }

    sl<NavigatorService>().push(AppRouter.tencentChat);
  }



  void onChangeShowNoticeDialog(bool flag) {
    emit(state.copyWith(showNoticeDialog: flag));
  }

  /// 注册送礼金弹窗倒计时timer
  // Timer? _signupBonusTimer;

  /// 是否已展示提醒注册弹窗， 默认false
  bool hasShownSignupDialog = false;

  void startSignupBonusTimer() {
    // if (sl<UserCubit>().state.isLogin || hasShownSignupDialog) return;
    // _signupBonusTimer?.cancel();

    // /// 不显示的路由
    // List<String> disableRouterList = [AppRouter.login, AppRouter.register];
    // if (kIsWeb) {
    //   disableRouterList.addAll([AppRouter.commonWebView, AppRouter.commonHtmlView]);
    // }

    // _signupBonusTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
    //   final route = RouteTracker().getCurrentRouteName();

    //   if (!sl<UserCubit>().state.isLogin && !hasShownSignupDialog && !disableRouterList.contains(route)) {
    //     hasShownSignupDialog = true;
    //     SignupBonusDialog().show(
    //       sl<NavigatorService>().navigatorKey.currentState!.context,
    //     );
    //     _signupBonusTimer?.cancel();
    //   }
    // });
  }

  @override
  Future<void> close() {
    // _signupBonusTimer?.cancel();
    return super.close();
  }
}
