import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';

import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';

import 'package:wd/features/page/login/register/register_state.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/sheet/currency_picker_sheet.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/verification_code/verification_code.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';

import '../../../../core/constants/enums.dart';
import 'register_cubit.dart';

class RegisterPage extends StatefulWidget {
  final String? inviteCode;
  final String? channelCode;

  const RegisterPage({
    super.key,
    this.inviteCode,
    this.channelCode,
  });

  @override
  State<StatefulWidget> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> with HideFloatButtonRouteAwareMixin {
  static const double _sectionSpacing = 30.0;
  static const double _kVerticalSpacing = 15.0;

  // Page-scoped controllers
   final TextEditingController usernameController = TextEditingController();
   final TextEditingController passwordController = TextEditingController();
   final TextEditingController confirmPasswordController = TextEditingController();
   final TextEditingController verificationCodeController = TextEditingController();
   final TextEditingController phoneController = TextEditingController();
   final TextEditingController emailController = TextEditingController();
   final TextEditingController smsCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<RegisterCubit>().initConfig();

    WidgetsBinding.instance.addPostFrameCallback(_initializeData);
  }

  void _initializeData(_) {
    _initializeInviteCode();
    _initializeChannelCode();
  }
  
  void _clearControllers() {
    usernameController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    verificationCodeController.clear();
    phoneController.clear();
    emailController.clear();
    smsCodeController.clear();
  }

  @override
  void dispose() {
    usernameController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    verificationCodeController.dispose();
    phoneController.dispose();
    emailController.dispose();
    smsCodeController.dispose();
    super.dispose();
  }

  Future<void> _initializeInviteCode() async {
    final code = widget.inviteCode ?? GlobalConfig().inviteCode ?? (kDebug ? "10000001" : "88888888");

    context.read<RegisterCubit>().setInviteCode(code);
  }

  Future<void> _initializeChannelCode() async {
    final code = widget.channelCode ?? GlobalConfig().channelCode;
    if (code != null) {
      context.read<RegisterCubit>().setChannelCodeCode(code);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterCubit, RegisterState>(
      listenWhen: _registerStateListenWhen,
      listener: _handleRegisterStateChanges,
      child: BlocBuilder<RegisterCubit, RegisterState>(
        builder: (context, state) {
          if (state.registerType == LoginType.phone && !state.authMethodType.supportsPhone) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _clearControllers();
              context.read<RegisterCubit>().switchRegisterType();
            });
          } else if (state.registerType == LoginType.userName && !state.authMethodType.supportsAccount) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _clearControllers();
              context.read<RegisterCubit>().switchRegisterType();
            });
          }

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildRegistrationForm(state.registerType),
            ],
          );
        },
      ),
    );
  }

  bool _registerStateListenWhen(RegisterState previous, RegisterState current) {
    return previous.registerStatus != current.registerStatus || previous.captchaModel?.img != current.captchaModel?.img;
  }

  void _handleRegisterStateChanges(BuildContext context, RegisterState state) {
    // Handle captcha image if needed
  }

  Widget _buildRegistrationForm(LoginType type) {
    switch (type) {
      case LoginType.userName:
        return _buildPasswordRegisterForm();
      case LoginType.phone:
        return _buildPhoneRegisterForm();
      case LoginType.email:
        return _buildEmailRegisterForm();
      default:
        return _buildPasswordRegisterForm();
    }
  }

  Widget _buildPasswordRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            // Input fields with staggered animation
            CommonScaleAnimationWidget(
              children: [
                _buildUsernameInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildConfirmPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildCurrencyWidget(context, selectCurrency: state.selectCurrency),
                SizedBox(height: _kVerticalSpacing.gw),
                if (kEnablePrivacyPolicySwitch) ...[
                  _buildPrivacyPolicySwitch(state),
                  SizedBox(height: _sectionSpacing.gw),
                ],

                // Sign Up button with scale animation
                AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 300),
                  child: ScaleAnimation(
                    scale: 0.9,
                    child: _buildRegisterButton(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.gw),

            // Signup method buttons
            _buildSignupMethodButtons(state),
            SizedBox(height: 20.gw),
          ],
        );
      },
    );
  }

  Widget _buildPhoneRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            // Input fields with staggered animation
            CommonScaleAnimationWidget(
              children: [
                _buildPhoneInputField(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildSmsVerificationInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildConfirmPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildCurrencyWidget(context, selectCurrency: state.selectCurrency),
                SizedBox(height: _kVerticalSpacing.gw),
                if (kEnablePrivacyPolicySwitch) ...[
                  _buildPrivacyPolicySwitch(state),
                  SizedBox(height: _sectionSpacing.gw),
                ],

                // Sign Up button with scale animation
                AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 300),
                  child: ScaleAnimation(
                    scale: 0.9,
                    child: _buildRegisterButton(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.gw),

            // Signup method buttons
            _buildSignupMethodButtons(state),
          ],
        );
      },
    );
  }

  Widget _buildEmailRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            // Input fields with staggered animation
            CommonScaleAnimationWidget(
              children: [
                _buildEmailInputField(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildEmailVerificationInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildConfirmPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildCurrencyWidget(context, selectCurrency: state.selectCurrency),
                SizedBox(height: _kVerticalSpacing.gw),
                if (kEnablePrivacyPolicySwitch) ...[
                  _buildPrivacyPolicySwitch(state),
                  SizedBox(height: _sectionSpacing.gw),
                ],

                // Sign Up button with scale animation
                AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 300),
                  child: ScaleAnimation(
                    scale: 0.9,
                    child: _buildRegisterButton(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.gw),

            // Signup method buttons
            _buildSignupMethodButtons(state),
          ],
        );
      },
    );
  }

  /// Builds the username input field
  Widget _buildUsernameInput(RegisterState state) {
    return IconTextfield(
        controller: usernameController,
        hintText: "hint_register_username".tr(),
        inputFormatters: [
          /// 只允许 a-zA-Z0-9 和常见标点 !@#\$%^&*()_+-=[]{},.<>?/：
          FilteringTextInputFormatter.allow(
            RegExp(r'[a-zA-Z0-9!@#\$%^&*()_\+\-=\[\]{},.<>?/]+'),
          ),
        ],
        prefixIcon: IconButton(
          icon: Image.asset(Assets.iconLoginName, width: 20.gw, height: 20.gw),
          onPressed: () {},
        ),
        onChanged: (value) => context.read<RegisterCubit>().setUsername(value));
  }

  /// Builds the password input field with visibility toggle
  Widget _buildPasswordInput(RegisterState state) {
    return BlocBuilder<RegisterCubit, RegisterState>(
      buildWhen: (previous, current) => previous.isPasswordVisible != current.isPasswordVisible,
      builder: (context, state) {
        return IconTextfield(
          controller: passwordController,
          hintText: "password_rule".tr(),
          obscureText: !state.isPasswordVisible,
          prefixIcon: IconButton(
            icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
            onPressed: () {},
          ),
          suffixIcon: GestureDetector(
            onTap: () => context.read<RegisterCubit>().togglePasswordVisibility(),
            child: Container(
              width: 24.gw,
              height: 24.gw,
              alignment: Alignment.center,
              child: SvgPicture.asset(
                "assets/images/login/icon_password_${state.isPasswordVisible ? "" : "in"}visible.svg",
                width: 20.gw,
                height: 20.gw,
              ),
            ),
          ),
          onChanged: (value) => context.read<RegisterCubit>().setPassword(value),
        );
      },
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(RegisterState state) {
    return BlocBuilder<RegisterCubit, RegisterState>(
      buildWhen: (previous, current) => previous.isPasswordVisible != current.isPasswordVisible,
      builder: (context, state) {
        return IconTextfield(
          controller: confirmPasswordController,
          hintText: "password_rule".tr(),
          obscureText: !state.isPasswordVisible,
          prefixIcon: IconButton(
            icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
            onPressed: () {},
          ),
          suffixIcon: GestureDetector(
            onTap: () => context.read<RegisterCubit>().togglePasswordVisibility(),
            child: Container(
              width: 24.gw,
              height: 24.gw,
              alignment: Alignment.center,
              child: SvgPicture.asset(
                "assets/images/login/icon_password_${state.isPasswordVisible ? "" : "in"}visible.svg",
                width: 20.gw,
                height: 20.gw,
              ),
            ),
          ),
          onChanged: (value) => context.read<RegisterCubit>().setConfirmPassword(value),
        );
      },
    );
  }

  /// Builds the privacy policy acceptance switch
  Widget _buildPrivacyPolicySwitch(RegisterState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Transform.scale(
          scale: 0.6,
          alignment: Alignment.centerLeft,
          child: Switch(
            value: state.acceptPrivacyPolicy,
            onChanged: (value) => context.read<RegisterCubit>().toggleAcceptPrivacyPolicy(value),
            activeColor: context.theme.primaryColor,
            inactiveThumbColor: context.colorTheme.textSecondary,
            inactiveTrackColor: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
        Expanded(
          child: Text(
            'accept_privacy_policy'.tr(),
            style: context.textTheme.title,
          ),
        ),
      ],
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInputField(RegisterState state) {
    return PhoneInputField(
      controller: phoneController,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: state.selectedCountry,
      onCountryChanged: (country) {
        context.read<RegisterCubit>().updateSelectedCountry(country);
      },
      onChanged: (value) => context.read<RegisterCubit>().setPhone(value),
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField(RegisterState state) {
    return IconTextfield(
      controller: emailController,
      hintText: "hint_enter_email".tr(),
      prefixIcon: IconButton(
        icon: Image.asset(Assets.iconMail, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<RegisterCubit>().setEmail(value),
    );
  }

  /// Builds the signup method buttons based on current signup type
  Widget _buildSignupMethodButtons(RegisterState state) {
    final currentType = state.registerType;

    Widget? firstButton;
    Widget? secondButton;

    if (currentType == LoginType.userName) {
      if (state.authMethodType.supportsPhone) {
        firstButton = _buildSignupMethodButton(
          iconUrl: Assets.iconPhone3,
          title: "register_with_phone_number".tr(),
          onTap: () {
            _clearControllers();
            context.read<RegisterCubit>().switchRegisterType();
          },
        );
      }
      // Always show email option
      secondButton = _buildSignupMethodButton(
        iconUrl: Assets.iconEmail2,
        title: "register_with_email".tr(),
        onTap: () {
          _clearControllers();
          context.read<RegisterCubit>().switchToEmailRegister();
        },
      );
    } else if (currentType == LoginType.phone) {
      // Currently phone signup, show username and email options
      if (state.authMethodType.supportsAccount) {
        firstButton = _buildSignupMethodButton(
          iconUrl: Assets.iconUser,
          title: "register_with_username".tr(),
          onTap: () {
            _clearControllers();
            context.read<RegisterCubit>().switchRegisterType();
          },
        );
      }
      // Always show email option
      secondButton = _buildSignupMethodButton(
        iconUrl: Assets.iconEmail2,
        title: "register_with_email".tr(),
        onTap: () {
          _clearControllers();
          context.read<RegisterCubit>().switchToEmailRegister();
        },
      );
    } else if (currentType == LoginType.email) {
      if (state.authMethodType.supportsAccount) {
        firstButton = _buildSignupMethodButton(
          iconUrl: Assets.iconUser,
          title: "register_with_username".tr(),
          onTap: () {
            _clearControllers();
            context.read<RegisterCubit>().switchRegisterType();
          },
        );
      }
      if (state.authMethodType.supportsPhone) {
        secondButton = _buildSignupMethodButton(
          iconUrl: Assets.iconPhone3,
          title: "register_with_phone_number".tr(),
          onTap: () {
            _clearControllers();
            context.read<RegisterCubit>().switchToPhoneRegister();
          },
        );
      }
    }

    if (firstButton == null || secondButton == null) {
      return const SizedBox.shrink();
    }

    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 400),
      child: FadeInAnimation(
        child: Row(
          children: [
            Expanded(child: firstButton),
            SizedBox(width: 15.gw),
            Expanded(child: secondButton),
          ],
        ),
      ),
    );
  }

  /// Builds a single signup method button
  Widget _buildSignupMethodButton({
    required String iconUrl,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 50.gw,
        decoration: BoxDecoration(
          color: context.colorTheme.textSecondary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.gw),
          border: Border.all(
            color: context.colorTheme.textSecondary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppImage(
              fit: BoxFit.fill,
              imageUrl: iconUrl,
              height: 20.gw,
              width: 20.gw,
            ),
            SizedBox(width: 8.gw),
            Text(
              title,
              style: context.textTheme.primary.copyWith(
                color: Colors.white,
                fontSize: 14.gw,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegisterButton() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        final isEnabled = kEnablePrivacyPolicySwitch ? state.acceptPrivacyPolicy : true;

        return CommonButton(
          title: "sign_up".tr(),
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.registerStatus == NetState.loading,
          onPressed: isEnabled
              ? () {
                  final registerType = state.registerType;

                  if ((registerType == LoginType.phone && !state.authMethodType.supportsPhone) ||
                      (registerType == LoginType.userName && !state.authMethodType.supportsAccount)) {
                    GSEasyLoading.showToast('register_method_unavailable'.tr()); // 当前注册方式不可用
                    return;
                  }

                  // Dismiss keyboard using the same method as login
                  sl<NavigatorService>().unFocus();
                  context.read<RegisterCubit>().register(loginType: registerType);
                }
              : () {
                  GSEasyLoading.showToast("please_accept_privacy_policy".tr(), gravity: ToastGravity.BOTTOM);
                }, // Show toast when privacy policy not accepted
        );
      },
    );
  }

  /// Builds SMS verification input
  Widget _buildSmsVerificationInput(RegisterState state) {
    return IconTextfield(
      controller: smsCodeController,
      hintText: "hint_enter_verification_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: VerificationCode(
        phone: state.phone,
        checkIsBind: false,
        isGradient: false,
        areaCode: state.selectedCountry?.areaCode,
        onSmsCode: (smsCode) {
          if (kDebug && smsCode.isNotEmpty) {
            smsCodeController.text = smsCode;
            context.read<RegisterCubit>().setSmsCode(smsCode);
          }
        },
      ),
      onChanged: (value) => context.read<RegisterCubit>().setSmsCode(value),
    );
  }

  /// Builds email verification input
  Widget _buildEmailVerificationInput(RegisterState state) {
    return IconTextfield(
      controller: smsCodeController,
      hintText: "hint_enter_email_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: VerificationCode(
        phone: '',
        // Not used for email mode
        email: state.email,
        isEmailMode: true,
        checkIsBind: false,
        isGradient: false,
        onEmailCode: (emailCode) {
          if (kDebug && emailCode.isNotEmpty) {
            smsCodeController.text = emailCode;
            context.read<RegisterCubit>().setSmsCode(emailCode);
          }
        },
      ),
      onChanged: (value) => context.read<RegisterCubit>().setSmsCode(value),
    );
  }



  Widget _buildCurrencyWidget(BuildContext context, {CurrencyConfig? selectCurrency}) {
    if (selectCurrency == null) return const SizedBox.shrink();
    return InkWell(
      onTap: () {
        sl<NavigatorService>().unFocus();
        CurrencyPickerSheet(
          context,
          currentCurrency: selectCurrency,
          dataList: GlobalConfig().systemConfig.currencyList,
          onClickCurrency: (model) => context.read<RegisterCubit>().updateSelectedCurrency(model),
        ).show();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AneText("select_currency".tr(), style: context.textTheme.title.fs16),
          SizedBox(height: 10.gw),
          Container(
            height: 48.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            decoration: BoxDecoration(
              border: Border.all(
                color: context.theme.primaryColor,
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(12.gw),
            ),
            child: Row(
              children: [
                Expanded(
                    child: Text(
                  selectCurrency.name,
                  style: context.textTheme.title,
                )),
                SizedBox(
                  width: 10.gw,
                ),
                Icon(
                  Icons.arrow_drop_down,
                  size: 30.gw,
                  color: context.colorTheme.textTitle,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// Builds verification prefix icon
  Widget _buildVerificationPrefix() {
    return Padding(
      padding: EdgeInsets.all(18.gw),
      child: Image.asset(
        Assets.iconLoginShield,
        width: 20.gw,
        height: 24.gw,
      ),
    );
  }
}
