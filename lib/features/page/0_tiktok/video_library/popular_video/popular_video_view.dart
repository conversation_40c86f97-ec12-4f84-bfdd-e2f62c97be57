import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/models/entities/video_hot_tag_entity.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/0_tiktok/video_library/popular_video/popular_video_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/features/page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter.dart';
import 'package:wd/shared/widgets/dialog/video_age_warm_dialog.dart';
import 'package:wd/shared/widgets/tiktok/video_search_bar.dart';
import 'package:wd/shared/widgets/tiktok/video_silver_movie_grid_view.dart';
import 'package:wd/shared/widgets/tiktok/home_video_library_filter.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/models/entities/video_hot_movies_entity.dart';
import 'popular_video_filter/popular_video_filter_cubit.dart';

class PopularVideoPage extends StatefulWidget {
  const PopularVideoPage({super.key});

  @override
  State<StatefulWidget> createState() => _PopularVideoPageState();
}

class _PopularVideoPageState extends State<PopularVideoPage> with AutomaticKeepAliveClientMixin {
  final paddingH = 10.gw;
  late final topMargin = MediaQuery.of(context).padding.top + 10.gw;

  final RefreshController refreshController = RefreshController(initialRefresh: false);
  final TextEditingController searchController = TextEditingController();

  void _listener(BuildContext context, PopularVideoState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  void _onLoading(bool isGrouped) {
    if (!isGrouped) {
      context.read<PopularVideoFilterCubit>().updatePageNoToNext();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final cubit = context.read<PopularVideoCubit>();
    return BlocConsumer<PopularVideoCubit, PopularVideoState>(
        listener: _listener,
        builder: (context, state) {
          if (state.netState == NetState.showReload) {
            return Container(
                color: Theme.of(context).scaffoldBackgroundColor,
                child: NetErrorWidget(title: 'network_error'.tr(), refreshMethod: () => cubit.fetchHotVideo()));
          }
          final isGrouped = cubit.isPopular && state.searchKeyword.isEmpty;

          return Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: paddingH),
              child: CommonRefresher(
                bgColor: Theme.of(context).scaffoldBackgroundColor,
                enablePullDown: false,
                enablePullUp: !isGrouped,
                refreshController: refreshController,
                onRefresh: null,
                onLoading: () => _onLoading(isGrouped),
                listWidget: CustomScrollView(
                  slivers: [
                    // 固定的空白
                    SliverToBoxAdapter(child: SizedBox(height: topMargin)),
                    // 搜索框
                    SliverToBoxAdapter(
                        child: Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => sl<NavigatorService>().push(AppRouter.popularVideoSearch),
                            child: VideoSearchBar(
                              enable: false,
                              controller: searchController,
                            ),
                          ),
                        ),
                        SizedBox(width: 12.gw),
                        InkWell(
                          onTap: () {
                            VideoAgeWarmDialog(onTapConfirm: () {
                              sl<NavigatorService>().push(AppRouter.videoLibrary);
                            }).show(context);
                          },
                          child: Image.asset(
                            "assets/images/video/btn_18+.png",
                            width: 42.gw,
                            height: 42.gw,
                          ),
                        )
                      ],
                    )),
                    SliverToBoxAdapter(child: SizedBox(height: 15.gw)),
                    // 筛选视图
                    if (state.filterCategories.isNotEmpty) ...[
                      SliverToBoxAdapter(
                        child: AnimationLimiter(
                          child: HomeVideoLibraryFilterView(
                            current: state.currentVideoCategory,
                            dataList: state.filterCategories,
                            onFilterSelected: (item) {
                              cubit.onSelectCategoryFilter(item);
                            },
                          ),
                        ),
                      ),
                    ],
                    // SliverToBoxAdapter(
                    //   child: AnimationLimiter(
                    //     key: const ValueKey(0),
                    //     child: PopularVideoFilter(
                    //         dataList: const [],
                    //         moviesCategory: [
                    //           VideoHotTagMoviesCategory()
                    //             ..dictKey = 'all'
                    //             ..dictValue = '热门',
                    //           ...(state.videoHotTags?.moviesCategory ?? []),
                    //         ],
                    //         selectedItem: state.categoryFilter,
                    //         onFilterSelected: (item) {
                    //           context.read<PopularVideoFilterCubit>().onSelectCategoryFilter(item: item);
                    //         }),
                    //   ),
                    // ),

                    if (state.netState == NetState.empty) ...[
                      SliverToBoxAdapter(
                          child: SizedBox(
                        height: 400.gw,
                        child: const EmptyWidget(),
                      )),
                    ] else ...[
                      // 视频列表
                      SliverPadding(
                        padding: EdgeInsets.only(top: 15.gw),
                        sliver: VideoSliverMovieGridView(
                          videoList: state.moviesListMap ?? VideoHotMoviesEntity(),
                          filterVideoList: state.filterVideoList,
                          isGrouped: isGrouped,
                          onTapCell: (videoEntity) {
                            final model = VideoListRecords()
                              ..id = videoEntity.id ?? 0
                              ..videoImage = videoEntity.videoImage ?? ''
                              ..videoTitle = videoEntity.videoTitle ?? ''
                              ..videoTime = videoEntity.videoTime ?? ''
                              ..videoYear = videoEntity.videoYear ?? ''
                              ..videoCategory = videoEntity.videoCategory ?? ''
                              ..videoType = videoEntity.videoType ?? 0
                              ..videoTags = videoEntity.videoTags ?? ''
                              ..videoCountry = videoEntity.videoCountry ?? ''
                              ..videoClarity = videoEntity.videoClarity ?? ''
                              ..videoBottomTag = videoEntity.videoBottomTag ?? ''
                              ..playCount = videoEntity.playCount ?? 0
                              ..hide = videoEntity.hide ?? 0
                              ..createTime = videoEntity.createTime ?? '';
                            sl<NavigatorService>().push(
                              AppRouter.videoDetail,
                              arguments: {
                                "model": model,
                                "videoCategory": videoEntity.videoCategory,
                                "pageTitle": '热门电影',
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        });
  }

  @override
  bool get wantKeepAlive => true;
}
