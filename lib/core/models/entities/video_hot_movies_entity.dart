import 'package:wd/generated/json/base/json_field.dart';
import 'dart:convert';
import 'package:wd/generated/json/video_hot_movies_entity.g.dart';


@JsonSerializable()
class VideoHotMoviesEntity {
  Map<String, List<VideoHotMovies>>? list;

  VideoHotMoviesEntity();

  factory VideoHotMoviesEntity.fromJson(Map<String, dynamic> json) {
    return VideoHotMoviesEntity()
      ..list = json.map((key, value) => MapEntry(
          key,
          (value as List)
              .map((item) => VideoHotMovies.fromJson(item))
              .toList()));
  }

  Map<String, dynamic> toJson() =>
      list?.map((key, value) => MapEntry(key, value.map((item) => item.toJson()).toList())) ?? {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class VideoHotMovies {
  int id = 0;
  String videoImage = '';
  String videoTitle = '';
  dynamic videoYear;
  String videoCategoryCode = '';
  String videoCategory = '';
  int videoType = 0;
  String videoTags = '';
  dynamic videoCountry;
  String videoClarity = '';
  String videoBottomTag = '';
  int playCount = 0;
  int baseLikes = 0;
  int hide = 0;
  dynamic createTime;
  dynamic thirdVideoId;
  int? isException;
  int? isPinned;
  dynamic videoTime;

  VideoHotMovies();

  factory VideoHotMovies.fromJson(Map<String, dynamic> json) => $VideoHotMoviesFromJson(json);

  Map<String, dynamic> toJson() => $VideoHotMoviesToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
