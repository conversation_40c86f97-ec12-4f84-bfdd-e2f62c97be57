

import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/activity_category_entity.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';


// 兼容别名（可在业务侧逐步替换）
typedef ActivityGameTypeViewModel = ActivityTypeViewModel<ActivityRecords>;
typedef ActivityTaskTypeViewModel = ActivityTypeViewModel<ActivityTask>;

class ActivityTypeViewModel<T> extends Equatable {
  final int category;
  final String categoryName;
  final String icon;
  final int pageNo;
  final List<T> list;
  final NetState netState;
  final bool? isNoMoreDataState;

  const ActivityTypeViewModel({
    required this.category,
    required this.categoryName,
    required this.icon,
    this.pageNo = 1,
    this.list = const [],
    this.netState = NetState.idle,
    this.isNoMoreDataState,
  });

  factory ActivityTypeViewModel.fromActivityCategory(ActivityCategory model) => ActivityTypeViewModel<T>(
        category: model.category,
        categoryName: model.categoryName,
        icon: model.icon,
      );

  ActivityTypeViewModel<T> copyWith({
    int? pageNo,
    List<T>? list,
    NetState? netState,
    bool? isNoMoreDataState,
  }) {
    return ActivityTypeViewModel<T>(
      category: category,
      categoryName: categoryName,
      icon: icon,
      pageNo: pageNo ?? this.pageNo,
      list: list ?? this.list,
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
    );
  }

  @override
  List<Object?> get props => [category, categoryName, icon, pageNo, list, netState, isNoMoreDataState];
}
