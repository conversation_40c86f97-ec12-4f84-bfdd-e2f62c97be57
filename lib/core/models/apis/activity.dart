import 'package:wd/core/models/entities/activity_category_entity.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/entities/activity_task_record_entity.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/utils/http/https.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

class ActivityApi {
  /// Activity 活动分类列表, type活动类型 1-游戏活动 2-自助领取
  static Future<List<T>> fetchActivityCategoryList<T>(
    int type, {
    required T Function(ActivityCategory) mapper,
  }) async {
    ResponseModel res = await Http().request<ActivityCategoryListEntity>(
      ApiConstants.activityCategoryList,
      params: {
        "activeType": type // 活动类型 1-游戏活动 2-自助领取
      },
      method: HttpMethod.get,
      needSignIn: false,
    );
    if (res.isSuccess && res.data != null) {
      final list = res.data.list;
      if (list is List<ActivityCategory>) {
        return list.map((e) => mapper(e)).toList();
      }
    }
    return [];
  }

  /// Activity 活动列表
  static Future<ActivityListEntity> fetchActivityList({required int pageNo, required int activeCategory}) async {
    ResponseModel res = await Http().request<ActivityListEntity>(
      ApiConstants.activityList,
      params: {
        "pageNo": pageNo,
        "pageSize": 20,
        "activeCategory": activeCategory,
      },
      needSignIn: false,
    );
    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return ActivityListEntity();
    }
  }

  /// Activity 活动详情
  static Future<ActivityRecords?> fetchActivityDetail({required int id}) async {
    ResponseModel res = await Http().request<ActivityRecords>(
      ApiConstants.activityInfo,
      method: HttpMethod.get,
      params: {"actId": id},
      needSignIn: false,
    );

    return res.data;
  }

  /// Task 自助领取活动列表
  static Future<List<ActivityTask>> fetchTaskList({required int activeCategory}) async {
    final res = await Http().request<ActivityTaskListEntity>(
      ApiConstants.taskList,
      params: {"activeCategory": activeCategory},
      method: HttpMethod.get,
    );
    if (res.isSuccess && res.data != null) {
      final model = res.data!.list;
      return model;
    }
    return [];
  }

  /// 获取彩金领取状态 0:未领取,1:已领取,2:已过期, -1未知
  static Future<int> fetchActivityCollectionStatus({required int id}) async {
    final res = await Http().request(
      ApiConstants.activityCollectionStatus,
      params: {'id': id},
    );
    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return -1;
  }

  static Future<bool> completeActivityTask({required int id}) async {
    final res = await Http().request(
      ApiConstants.activityTaskComplete, // New endpoint
      params: {"id": id},
      method: HttpMethod.get,
    );
    return res.isSuccess;
  }

  static Future<({bool isSuccess, String msg})> applyActivity({required int id, required int operationType}) async {
    final res = await Http().request(ApiConstants.activityApply,
        params: {
          "activeId": id,
          "operationType": operationType,
        },
        needShowToast: false);
    return (isSuccess: res.isSuccess, msg: res.msg ?? "");
  }

  // 自助活动领取记录
  static Future<ActivityTaskRecordEntity?> fetchActivityTaskRecordList({required int pageNo}) async {
    final res = await Http().request<ActivityTaskRecordEntity>(
      ApiConstants.activityTaskRecordList,
      params: {
        "pageNo": pageNo,
        "pageSize": 20,
      },
    );

    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return null;
  }

  /// 幸运礼包-领取任务
  static Future<HomeFeedLuckyGift?> claimLuckyGiftTask() async {
    GSEasyLoading.showLoading();
    try {
      final res = await Http().request<HomeFeedLuckyGift>(ApiConstants.claimLuckyGiftTask);
      if (res.isSuccess && res.data != null) {
        return res.data;
      }
      return null;
    } catch (e) {
      return null;
    } finally {
      GSEasyLoading.dismiss();
    }
  }

  /// 幸运礼包-领取佣金
  static Future<bool> claimLuckyGiftBonus() async {
    GSEasyLoading.showLoading();
    try {
      final res = await Http().request(ApiConstants.claimLuckyGiftBonus);
      return res.isSuccess;
    } catch (e) {
      return false;
    } finally {
      GSEasyLoading.dismiss();
    }
  }
}
