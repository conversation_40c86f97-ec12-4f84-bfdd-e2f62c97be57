import 'package:wd/core/models/entities/home_banner_entity.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/utils/http/https.dart';

class HomeApi {
  /// 获取首页轮播图列表
  static Future<List<HomeBannerEntity>> fetchHomeBannerList() async {
    final res = await Http().request<HomeBannerListEntity>(ApiConstants.homeBannerList, needSignIn: false);
    if (res.isSuccess && res.data != null) {
      return res.data!.list;
    }
    return [];
  }


  /// 获取首页活动弹窗、跑马灯、幸运活动等相关
  static Future<HomeFeedEntity?> fetchHomeFeedList() async {
    ResponseModel response = await Http().request<HomeFeedEntity>(
      ApiConstants.homeFeedList,
      needSignIn: false,
    );
    return response.data;
  }

  /// 获取首页活动弹窗、跑马灯、幸运活动等相关
  static Future<HomeFeedEntity?> fetchHomeActivityActionList() async {
    ResponseModel response = await Http().request<HomeFeedEntity>(
      ApiConstants.homeActivityActionList,
      method: HttpMethod.get,
    );
    return response.data;
  }
}
