import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/agent.dart';
import 'package:wd/core/models/apis/notification.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/models/entities/invite_code_entity.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/models/entities/user_info_entity.dart';
import 'package:wd/core/models/entities/user_vip_entity.dart';
import 'package:wd/core/models/entities/video_entity.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/singletons/local_storage_manager.dart';
import 'package:wd/core/utils/dialog_queue_manager/dialog_queue_manager.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/utils/polling_services/polliing_services.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/features/page/1_game_home/game_home_cubit.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/3_chat/chat_cubit.dart';
import 'package:wd/features/page/4_mine/notifications/notification_cubit.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../models/entities/login_entity.dart';
import 'user_state.dart';

class UserCubit extends Cubit<UserState> {
  UserCubit() : super(UserState()) {
    loadUser();
  }

  void setLoginInfo(LoginTokenUser? entity) {
    emit(state.copyWith(
      loginInfo: entity,
      isLogin: entity != null,
    ));
    fetchVideoWatchLimit();
    if (entity != null) {
      DialogQueueManager().clearQueue();
      fetchTencentConfig();
      uploadRemotePushToken();
      LocalStorageManager().saveObject(LocalStorageKey.loginInfo, entity.toJson());
      fetchUserInfo();
      fetchUserBalance();
      fetchUserVip();
      fetchInviteInfo();
      fetchVideoVipInfo();
      Future.delayed(const Duration(milliseconds: 500)).then((_) {
        startWithdrawOrderListPolling();
      });

      if (sl.isRegistered<NotificationsCubit>()) {
        sl<NotificationsCubit>().fetchNotifications(updateCount: true, reset: true);
      }
    } else {
      _clearUser();
    }
  }

  void setUserInfo(UserInfoEntity? entity) {
    emit(state.copyWith(userInfo: () => entity));
    if (entity != null) {
      LocalStorageManager().saveObject(LocalStorageKey.userInfo, entity.toJson());
    } else {
      _clearUser();
    }
  }

  void setBalanceInfo(UserBalanceEntity? balanceInfo) {
    emit(state.copyWith(balanceInfo: balanceInfo));
  }

  void setVipInfo(UserVipEntity? vipInfo) {
    emit(state.copyWith(vipInfo: vipInfo));
  }

  void setInviteInfo(InviteCodeEntity? inviteInfo) {
    emit(state.copyWith(inviteInfo: inviteInfo));
  }

  void setVideoVipInfo(VideoVipRemainDayEntity? videoVipInfo) {
    emit(state.copyWith(videoVipInfo: videoVipInfo));
  }

  String? _videoWatchLimit;

  String get videoWatchLimit => _videoWatchLimit ?? "3,5,1,3"; // 短视频未登录观看条数，短视频已登录观看条数，长视频未登录观看分钟，长视频登录观看分钟

  int get shortVideoCountLimit {
    final videoWatchLimitList = videoWatchLimit.split(',').map((e) => int.tryParse(e) ?? 3).toList();
    return state.isLogin ? videoWatchLimitList[1] : videoWatchLimitList[0];
  }

  int get filmMinutesLimit {
    final videoWatchLimitList = videoWatchLimit.split(',').map((e) => int.tryParse(e) ?? 1).toList();
    return state.isLogin ? videoWatchLimitList[3] : videoWatchLimitList[2];
  }

  CurrencyConfig? getCurrentCurrency() {
    if (state.userInfo == null) return null;
    final currencyList = GlobalConfig().systemConfig.currencyList;
    return currencyList.firstWhereOrNull((e) => e.id == state.userInfo!.currencyId);
  }


  /// 设置当前币种 / Set currency
  void setCurrentCurrency(CurrencyConfig entity) async {
    if (!state.isLogin) return;
    if (state.userInfo!.currencyId == entity.id) return;
    GSEasyLoading.showLoading();
    final flag = await  UserApi.updateCurrency(entity.id);
    GSEasyLoading.dismiss();
    if (flag) {
      DialogQueueManager().clearQueue();
      sl<GameHomeCubit>().fetchDialogNMarqueeListList(); // 获取系统公告弹窗
      fetchUserInfo();
      fetchUserBalance();
      fetchUserVip();
      fetchInviteInfo();
      sl<GameHomeCubit>().fetchData(forcedUpdate: true);
      sl<ActivityListCubit>().reset();
    }
  }

  uploadRemotePushToken() {
    final token = state.remotePushDeviceToken;
    if (token != null) {
      NotificationApi.uploadDeviceToken(token);
    }
  }

  /// 记录已进入游戏id，用于转出余额
  List<int> loggedInPlatformIds = [];

  void addPlatformId(int platformId) async {
    if (!loggedInPlatformIds.contains(platformId)) {
      loggedInPlatformIds.add(platformId);
      LocalStorageManager().saveStringList(
        LocalStorageKey.loggedInPlatformIds,
        loggedInPlatformIds.map((e) => e.toString()).toList(),
      );
    }
  }

  void removePlatformId(int platformId) async {
    if (loggedInPlatformIds.contains(platformId)) {
      loggedInPlatformIds.remove(platformId);
      LocalStorageManager().saveStringList(
        LocalStorageKey.loggedInPlatformIds,
        loggedInPlatformIds.map((e) => e.toString()).toList(),
      );
    }
  }

  fetchUserInfo() async {
    if (!state.isLogin) return;
    final result = await UserApi.fetchUserInfo();
    if (result != null && state.isLogin) {
      if (!result.tiktokTabVisible && sl<MainScreenCubit>().state.currentTabType == BottomNavType.videoHome) {
        sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.gameHome);
      }
      if (!StringUtil.isEmpty(result.language)) {
        LocaleUtil().setupRemoteLocale(result.language);
      }
      setUserInfo(result);
    }
  }

  fetchUserBalance() async {
    if (!state.isLogin) return;
    final result = await UserApi.fetchBalanceInfo();
    if (result != null && state.isLogin) setBalanceInfo(result);
  }

  fetchUserVip() async {
    if (!state.isLogin) return;
    final result = await UserApi.fetchVipInfo();
    if (result != null && state.isLogin) setVipInfo(result);
  }

  fetchInviteInfo() async {
    if (!state.isLogin) return;
    final result = await AgentApi.fetchInviteCodeInfo();
    if (result != null && state.isLogin) setInviteInfo(result);
  }

  fetchVideoVipInfo() async {
    if (!state.isLogin) return;
    final result = await VideoApi.fetchRemainingVipDays();
    if (result != null && state.isLogin) setVideoVipInfo(result);
  }

  fetchVideoWatchLimit() async {
    var res = await GlobalConfig().getConfigValueByKey("video_watch_limit");
    if (res != null && state.isLogin) {
      _videoWatchLimit = res;
    }
  }

  Future<bool> fetchTencentConfig() async {
    final res = await UserApi.fetchTencentUserSig();
    if (res != null && state.loginInfo != null) {
      emit(state.copyWith(tencentConfig: res));
      sl<ChatCubit>().initChats(int.parse(res.sdkAppId), res.userId, res.userSig, state.loginInfo!.token);
    }
    return res != null;
  }

  int _fundPwdErrorCount = 0;
  DateTime? _fundPwdErrorTime;
  Duration lockDuration = const Duration(minutes: 1);

  bool get isFundPasswordInputLocked {
    if (_fundPwdErrorTime == null) return false;
    if (_fundPwdErrorCount < 5) return false;

    final diff = DateTime.now().difference(_fundPwdErrorTime!);
    if (diff >= lockDuration) {
      resetFundPasswordErrorLock(); // 自动解锁
      return false;
    }
    return true;
  }

  void onFundPasswordError() {
    _fundPwdErrorTime = DateTime.now();
    _fundPwdErrorCount++;
    saveFundPwdErrorRecord(_fundPwdErrorCount, _fundPwdErrorTime);
  }

  void resetFundPasswordErrorLock() {
    _fundPwdErrorCount = 0;
    _fundPwdErrorTime = null;
    saveFundPwdErrorRecord(_fundPwdErrorCount, _fundPwdErrorTime);
  }

  // 初始化时读取本地记录（只在loadUser调用）
  Future<void> loadFundPwdErrorRecord() async {
    final record = await LocalStorageManager().getObject(LocalStorageKey.fundPwdError, (json) => json);
    if (record != null) {
      _fundPwdErrorCount = record['count'] ?? 0;
      final timeStr = record['time'] as String?;
      _fundPwdErrorTime = (timeStr != null && timeStr.isNotEmpty) ? DateTime.tryParse(timeStr) : null;
    } else {
      _fundPwdErrorCount = 0;
      _fundPwdErrorTime = null;
    }
  }

  Future<void> saveFundPwdErrorRecord(int count, DateTime? time) async {
    await LocalStorageManager().saveObject(LocalStorageKey.fundPwdError, {
      'count': count,
      'time': time?.toIso8601String(),
    });
  }

  void startWithdrawOrderListPolling() {
    sl<PollingService>().startPolling(
      id: kGSOperationWithdrawOrderPolling,
      onPoll: () async {
        fetchWithdrawOrderList();
        return true;
      },
      interval: const Duration(seconds: 60),
      shouldStop: () => !state.isLogin,
      shouldPause: () => RouteTracker().getCurrentRouteName() != AppRouter.nav,
    );
  }

  fetchWithdrawOrderList() async {
    final dateRange = TimeUtil.getDateRange(RecordDateType.week);
    final result = await TransactApi.fetchWithdrawHistoryList(
      startDate: dateRange.$1,
      endDate: dateRange.$2,
    );
    WithdrawRecord? record;
    if (result != null && result.records.isNotEmpty) {
      record = result.records.first;
    }
    onChangeWithdrawFloatBtnTitle(record);
  }

  onChangeWithdrawFloatBtnTitle(WithdrawRecord? record) {
    String title = "";
    if (record != null) {
      /// 第一条状态为审核中时，悬浮按钮一直显示
      /// 第一条状态为通过、驳回、取消，未读时，悬浮按钮一直显示
      if (record.orderStatus == 0) title = "等待审核中..";
      if (record.orderStatus > 0 && record.read == false) title = "已完成";
    }
    emit(state.copyWith(withdrawFloatBtnTitle: title));
  }

  Future<void> loadUser() async {
    loggedInPlatformIds = (await LocalStorageManager().getStringList(LocalStorageKey.loggedInPlatformIds))
        .map((id) => int.parse(id))
        .toList();

    final userInfo =
        await LocalStorageManager().getObject(LocalStorageKey.userInfo, (json) => UserInfoEntity.fromJson(json));
    if (userInfo != null) setUserInfo(userInfo);

    final loginInfo =
        await LocalStorageManager().getObject(LocalStorageKey.loginInfo, (json) => LoginTokenUser.fromJson(json));
    if (loginInfo != null) setLoginInfo(loginInfo);


    await loadFundPwdErrorRecord();
  }

  Future<void> _clearUser() async {
    // TencentImUtil.resetSDK();
    sl<ChatCubit>().logout();
    DialogQueueManager().clearQueue();
    LocalStorageManager().destroyAllStorage();
    emit(UserState());
    if (sl.isRegistered<NotificationsCubit>()) {
      sl<NotificationsCubit>().resetState();
    }
    sl<PollingService>().stopAll();
    sl<MainScreenCubit>().startSignupBonusTimer();
  }
}
