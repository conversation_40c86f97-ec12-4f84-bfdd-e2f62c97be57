import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/dialog/hot_event/hot_event_dialog.dart';
import 'package:wd/shared/widgets/dialog/lucky_gift/lucky_gift_dialog.dart';
import 'package:wd/shared/widgets/dialog/notification_alert_dialog.dart';
import 'package:wd/shared/widgets/dialog/welcome/welcome_bonus_dialog.dart';

/// 弹窗队列管理器 - 单例模式

import 'package:flutter/foundation.dart';

/// 弹窗类型
enum DialogType { notice, luckyGift, popular }

/// 配置读取的轻量包装
class _DialogSetting {
  final int sort;
  final int intervalSeconds;
  const _DialogSetting(this.sort, this.intervalSeconds);
}

/// 内部弹窗信息（尽量不可变）
class _DialogInfo {
  final DialogType type;
  final dynamic data;
  final int sort;
  final int interval; // seconds
  final bool isShown;

  const _DialogInfo({
    required this.type,
    required this.data,
    required this.sort,
    required this.interval,
    this.isShown = false,
  });

  _DialogInfo copyWith({
    dynamic data,
    int? sort,
    int? interval,
    bool? isShown,
  }) {
    return _DialogInfo(
      type: type,
      data: data ?? this.data,
      sort: sort ?? this.sort,
      interval: interval ?? this.interval,
      isShown: isShown ?? this.isShown,
    );
  }
}

/// 队列管理器（单例）
class DialogQueueManager {
  DialogQueueManager._();
  static final DialogQueueManager _inst = DialogQueueManager._();
  factory DialogQueueManager() => _inst;

  // 状态
  bool _paused = false;
  bool _processing = false; // 防止并发重入
  bool _hasIngested = false; // 是否已有数据进入
  bool _isShowing = false;

  bool get isQueuePaused => _paused;
  bool get isProcessing => _processing;
  bool get hasDataProcessed => _hasIngested;
  bool get isShowingDialog => _isShowing;

  final List<_DialogInfo> _dialogQueue = [];

  int _pendingIntervalSec = 0; // 上一个弹窗的间隔（秒），在下一轮循环开始时消化

  bool get _isUserLogin => sl<UserCubit>().state.isLogin;

  /// 入口：合并数据并尝试跑队列
  Future<void> processDialogQueue(HomeFeedEntity model) async {
    debugPrint('DQ: ingest, login=$_isUserLogin, hasIngested=$_hasIngested');

    // 未登录只处理 welcome
    if (!_isUserLogin) {
      if (model.welcomeList.isNotEmpty) {
        await WelcomeBonusDialog(data: model.welcomeList).show();
      }
      _hasIngested = true;
      return;
    }

    // 登录：合并到队列
    _mergeIn(model);

    // 标记有数据
    _hasIngested = true;

    // 如果没在处理且未暂停，则开始抽队列
    if (!_processing && !_paused) {
      await _drainQueue();
    }
  }

  /// 手动展示某类型（可传 data 或 model）
  Future<void> showDialogByType(
      DialogType type, {
        dynamic data,
        HomeFeedEntity? model,
      }) async {
    if (_isShowing) {
      debugPrint('DQ: showing in progress, skip manual.');
      return;
    }

    _isShowing = true;
    pauseQueue();
    try {
      dynamic payload = data ?? _extractFromModel(model, type) ?? _findDataInQueue(type);
      if (payload == null) {
        debugPrint('DQ: manual show $type but no data.');
        return;
      }

      await _show(type, payload);

      // 标记已展示，避免后续重复
      _markShown(type);
    } finally {
      _isShowing = false;
      await resumeQueue();
    }
  }

  Future<void> showDialog(DialogType type) => showDialogByType(type);

  /// 暂停/恢复/清空
  void pauseQueue() {
    if (_paused) return;
    _paused = true;
    debugPrint('DQ: paused');
  }

  Future<void> resumeQueue() async {
    if (!_paused) return;
    _paused = false;
    debugPrint('DQ: resumed');
    if (!_processing && _dialogQueue.any((e) => !e.isShown)) {
      await _drainQueue();
    }
  }

  void clearQueue() {
    debugPrint('DQ: clear');
    _pendingIntervalSec = 0;
    _dialogQueue.clear();
    _paused = false;
    _processing = false;
    _isShowing = false;
    _hasIngested = false;
  }

  // ========================= 私有实现 =========================

  void _mergeIn(HomeFeedEntity model) {
    final List<_DialogInfo> incoming = [];

    // notice
    if (model.noticeList.isNotEmpty) {
      final s = _settingsOf(DialogType.notice);
      incoming.add(_DialogInfo(
        type: DialogType.notice,
        data: model.noticeList,
        sort: s.sort,
        interval: s.intervalSeconds,
      ));
    }

    // luckyGift
    if (model.luckyGift != null) {
      final s = _settingsOf(DialogType.luckyGift);
      incoming.add(_DialogInfo(
        type: DialogType.luckyGift,
        data: model.luckyGift!,
        sort: s.sort,
        interval: s.intervalSeconds,
      ));
    }

    // popular
    if (model.popularList.isNotEmpty) {
      final s = _settingsOf(DialogType.popular);
      incoming.add(_DialogInfo(
        type: DialogType.popular,
        data: model.popularList,
        sort: s.sort,
        interval: s.intervalSeconds,
      ));
    }

    // 合并（同 type 覆盖 data/sort/interval，保留 isShown 状态）
    for (final d in incoming) {
      final i = _dialogQueue.indexWhere((x) => x.type == d.type);
      if (i >= 0) {
        _dialogQueue[i] = _dialogQueue[i].copyWith(
          data: d.data,
          sort: d.sort,
          interval: d.interval,
        );
      } else {
        _dialogQueue.add(d);
      }
    }
  }

  /// 从队列中依次取出并展示弹窗
  ///
  /// 特点：
  /// - 串行：每次只展示一个弹窗，等待关闭后再展示下一个
  /// - 顺序：按照 sort 排序，优先级高的先展示
  /// - 可暂停：如果队列被暂停 (_paused=true)，则立即中止
  /// - 可恢复：resumeQueue() 会在暂停解除后自动继续抽队列
  /// - 容错：如果数据为空，也会标记为已展示，避免死循环
  /// - 防重入：用 _processing 标记，防止重复并发执行
  Future<void> _drainQueue() async {
    // 已暂停或正在执行中，直接返回
    if (_paused || _processing) return;

    _processing = true; // 进入处理状态
    try {
      // 循环消费队列
      while (!_paused) {
        // ① 前置等待：若上一个弹窗设置了间隔，先等待再取下一个
        if (_pendingIntervalSec > 0) {
          final gap = _pendingIntervalSec;
          await Future.delayed(Duration(seconds: gap));
          // _pendingIntervalSec = 0; // 先清零，避免重复等待
          if (_paused) break; // 等待过程中可能被暂停
        }

        // ② 取下一个待展示
        final next = _nextPending();
        if (next == null) break; // 没有待展示的弹窗，结束

        // ③ 记录“下一轮前置等待”的间隔（而不是现在等待）
        _pendingIntervalSec = next.interval;

        //  ④配置检查
        if (!_shouldShow()) {
          debugPrint('DQ: 配置不允许展示弹窗，跳过所有剩余队列');
          break;
        }

        // ⑤ 数据为空则跳过并标记已处理
        if (next.data == null) {
          _markShown(next.type);
          continue;
        }

        debugPrint(">>>>当前队列状态：_paused：$_paused");

        // ⑥ 展示弹窗
        _isShowing = true;
        await _show(next.type, next.data);
        _isShowing = false;

        // ⑦ 标记已展示
        _markShown(next.type);

      }
    } catch (e) {
      debugPrint('DQ: drain error $e');
    } finally {
      // 重置状态
      _processing = false;
      _isShowing = false;
    }
  }

  _DialogInfo? _nextPending() {
    final pending = _dialogQueue.where((e) => !e.isShown).toList()
      ..sort((a, b) => a.sort.compareTo(b.sort));
    return pending.isEmpty ? null : pending.first;
  }

  void _markShown(DialogType type) {
    final i = _dialogQueue.indexWhere((e) => e.type == type);
    if (i >= 0) {
      _dialogQueue[i] = _dialogQueue[i].copyWith(isShown: true);
    }
  }

  dynamic _findDataInQueue(DialogType type) {
    final i = _dialogQueue.indexWhere((e) => e.type == type);
    return i >= 0 ? _dialogQueue[i].data : null;
  }

  bool _shouldShow() {
    if (!_isUserLogin) return false;
    final ds = GlobalConfig().systemConfig.dialogSetting;
    if (ds == null) return false;
    return ds.notice != null || ds.luckyGift != null || ds.popular != null;
  }

  _DialogSetting _settingsOf(DialogType type) {
    final ds = GlobalConfig().systemConfig.dialogSetting;
    switch (type) {
      case DialogType.notice:
        return _DialogSetting(ds?.notice?.sort ?? 1, ds?.notice?.interval ?? 1);
      case DialogType.luckyGift:
        return _DialogSetting(ds?.luckyGift?.sort ?? 0, ds?.luckyGift?.interval ?? 1);
      case DialogType.popular:
        return _DialogSetting(ds?.popular?.sort ?? 2, ds?.popular?.interval ?? 1);
    }
  }

  dynamic _extractFromModel(HomeFeedEntity? model, DialogType type) {
    if (model == null) return null;
    switch (type) {
      case DialogType.notice:
        return model.noticeList.isNotEmpty ? model.noticeList : null;
      case DialogType.luckyGift:
        return model.luckyGift;
      case DialogType.popular:
        return model.popularList.isNotEmpty ? model.popularList : null;
    }
  }

  Future<void> _show(DialogType type, dynamic data) async {
    try {
      await Future.delayed(const Duration(milliseconds: 50)); // 缓冲 UI
      switch (type) {
        case DialogType.notice:
          await NotificationAlertDialog(data: data as List<HomeFeedItem>).show();
          break;
        case DialogType.luckyGift:
          await LuckyGiftDialog(model: data as HomeFeedLuckyGift).show();
          break;
        case DialogType.popular:
          await HotEventDialog(data: data as List<HomeFeedItem>).show();
          break;
      }
    } catch (e) {
      debugPrint('DQ: show error $e');
    }
  }
}



