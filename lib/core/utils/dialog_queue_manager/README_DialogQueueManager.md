# DialogQueueManager 弹窗队列管理器

## 概述

`DialogQueueManager` 是一个单例模式的弹窗队列管理器，用于统一管理应用中的各种弹窗显示。它根据用户的登录状态和系统配置来决定弹窗的显示顺序和间隔时间。

## 功能特性

- **单例模式**: 确保全局只有一个实例，避免重复显示弹窗
- **队列管理**: 支持弹窗队列，防止弹窗重叠显示
- **队列暂停/恢复**: 支持暂停和恢复弹窗队列，适用于特定场景
- **登录状态感知**: 根据用户登录状态显示不同的弹窗
- **配置驱动**: 根据 `GlobalConfig().systemConfig.dialogSetting` 配置决定弹窗顺序和间隔
- **自动排序**: 根据配置的 `sort` 字段自动排序弹窗
- **间隔控制**: 根据配置的 `interval` 字段控制弹窗间隔

## 弹窗类型

支持以下三种弹窗类型：

1. **WelcomeBonusDialog** (欢迎礼包) - 仅未登录用户显示
2. **NotificationAlertDialog** (公告弹窗) - 已登录用户显示
3. **LuckyGiftDialog** (幸运礼包) - 已登录用户显示
4. **HotEventDialog** (热门活动) - 已登录用户显示

## 使用方法

### 基本使用

```dart
import 'package:wd/core/utils/dialog_queue_manager.dart';
import 'package:wd/core/models/apis/home.dart';

// 获取首页数据并处理弹窗
Future<void> handleDialogs() async {
  final homeFeedData = await HomeApi.fetchHomeFeedList();
  
  if (homeFeedData != null) {
    await DialogQueueManager().processDialogQueue(homeFeedData);
  }
}
```

### 在Cubit中使用

```dart
class GameHomeCubit extends Cubit<GameHomeState> {
  
  /// 获取首页数据并处理弹窗
  void fetchNoticeList() async {
    final res = await HomeApi.fetchHomeFeedList();

    if (res != null) {
      // 使用DialogQueueManager处理弹窗队列
      await DialogQueueManager().processDialogQueue(res);
    }

    emit(state.copyWith(homeFeedEntity: () => res));
  }
}
```

### 检查弹窗状态

```dart
final dialogManager = DialogQueueManager();

// 检查是否正在显示弹窗
if (dialogManager.isShowingDialog) {
  print('当前正在显示弹窗');
}

// 获取队列长度
print('队列中还有 ${dialogManager.queueLength} 个弹窗任务');
```

### 暂停和恢复队列

```dart
// 暂停弹窗队列
DialogQueueManager().pauseQueue();

// 恢复弹窗队列
await DialogQueueManager().resumeQueue();

// 检查队列是否暂停
if (DialogQueueManager().isQueuePaused) {
  print('队列已暂停');
}

// 检查是否有待处理的任务
if (DialogQueueManager().hasPendingTask) {
  print('有任务等待处理');
}
```

### 使用DialogQueuePauseMixin

在需要在该界面显示弹窗的页面中使用mixin：

```dart
import 'package:wd/shared/mixin/dialog_queue_pause_mixin.dart';

class HomePage extends StatefulWidget {
  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with DialogQueuePauseMixin {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 页面内容...
      // 进入页面时自动恢复队列，退出时自动暂停
    );
  }
  
  // 如果需要手动控制
  void onSpecialEvent() {
    pauseDialogQueueManually(); // 手动暂停
    // 或
    resumeDialogQueueManually(); // 手动恢复
  }
}
```

**RouteAware 功能说明**：

Mixin 实现了 `RouteAware` 接口，能够自动响应路由变化：

- `didPush()`: 页面被推入时恢复队列（进入该界面）
- `didPop()`: 页面被弹出时暂停队列（离开该界面）
- `didPushNext()`: 当前页面被下一个页面覆盖时暂停队列
- `didPopNext()`: 当前页面重新可见时恢复队列

### 清空队列

```dart
// 清空所有弹窗队列
DialogQueueManager().clearQueue();
```

## 配置说明

### DialogIntervalSetting 配置结构

```dart
class DialogIntervalSetting {
  DialogInterval? luckyGift;  // 幸运礼包配置
  DialogInterval? notice;     // 公告弹窗配置
  DialogInterval? popular;    // 热门活动配置
}

class DialogInterval {
  int interval = 0;  // 间隔时间（秒）
  int sort = 0;      // 排序字段（数字越小越靠前）
}
```

### 配置示例

```json
{
  "notice_interval_set": {
    "notice": {
      "interval": 2,
      "sort": 1
    },
    "luckyGift": {
      "interval": 3,
      "sort": 2
    },
    "popular": {
      "interval": 1,
      "sort": 3
    }
  }
}
```

这个配置表示：
1. 先显示公告弹窗
2. 等待2秒后显示幸运礼包
3. 等待3秒后显示热门活动

## 工作流程

### 未登录状态
1. 只显示 `WelcomeBonusDialog` (欢迎礼包)
2. 不显示其他弹窗

### 已登录状态
1. 检查 `GlobalConfig().systemConfig.dialogSetting` 配置
2. 如果有配置：
   - 按 `sort` 字段排序弹窗
   - 按顺序显示弹窗
   - 根据 `interval` 字段添加间隔时间
3. 如果没有配置：
   - 按默认顺序显示：公告 → 幸运礼包 → 热门活动
   - 每个弹窗间隔1秒

### 队列处理
1. 如果队列暂停，新任务会保存为待处理任务
2. 如果当前正在显示弹窗，新任务会加入队列
3. 当前弹窗完成后，自动处理队列中的下一个任务
4. 队列恢复时，会先处理待处理任务，再处理队列中的任务
5. 支持手动清空队列

## 注意事项

1. **单例模式**: 不要手动创建 `DialogQueueManager` 实例，始终使用 `DialogQueueManager()`
2. **异步处理**: `processDialogQueue` 和 `resumeQueue` 是异步方法，需要使用 `await`
3. **错误处理**: 建议在使用时添加 try-catch 错误处理
4. **状态检查**: 在显示弹窗前可以检查 `isShowingDialog` 和 `isQueuePaused` 状态
5. **队列管理**: 在应用退出或页面切换时可以调用 `clearQueue()` 清空队列
6. **Mixin使用**: 在需要在该界面显示弹窗的页面中使用 `DialogQueuePauseMixin`
7. **生命周期**: Mixin会在页面的 `initState` 时恢复队列，在 `dispose` 时暂停队列
8. **路由感知**: 实现了 `RouteAware` 接口，能够响应路由变化自动管理队列状态

## 扩展性

如果需要添加新的弹窗类型：

1. 在 `DialogType` 枚举中添加新类型
2. 在 `_showDialogByType` 方法中添加对应的处理逻辑
3. 在 `_showDialogsByConfig` 方法中添加配置检查

## 常见使用场景

### 需要在该界面显示弹窗的页面

以下页面建议使用 `DialogQueuePauseMixin`：

1. **首页/主页面**: 需要显示各种弹窗通知
2. **活动页面**: 需要显示活动相关弹窗
3. **游戏大厅**: 需要显示游戏相关弹窗
4. **个人中心**: 需要显示用户相关弹窗
5. **通知页面**: 需要显示通知弹窗
6. **其他主要功能页面**: 需要显示相关弹窗

### 使用方法

```dart
class HomePage extends StatefulWidget {
  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with DialogQueuePauseMixin {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('首页')),
      body: HomeContent(),
    );
  }
}
```

## 示例代码

完整的使用示例请参考：
- `lib/shared/mixin/dialog_queue_pause_example.dart` - 各种使用场景的示例
- `lib/shared/mixin/dialog_queue_pause_mixin.dart` - Mixin的具体实现
