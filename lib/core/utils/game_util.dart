import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/core/models/apis/lottery.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/models/entities/platform_amount_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/dialog_queue_manager/dialog_queue_manager.dart';
import 'package:wd/core/utils/file_cache_manager/file_cache_manager.dart';
import 'package:wd/core/utils/game_home_util.dart';
import 'package:wd/core/utils/game_recently_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'log_util.dart';

class GameUtil {
  static final notShowPlatformTab = ["SX", "TY", "DJ", "CP", "YLQP"];

  static final GameUtil _instance = GameUtil._internal();

  static List<int> favGameIdList = [];

  factory GameUtil() => _instance;

  GameUtil._internal();

  Map<String, String> platformInfoMap = {};

  List<GameTypeV2> gameList = [];
  List<LotteryGroup> lotteryGroupList = [];

  Future<List<GameTypeV2>>? _fetchListFuture;

  /// 获取游戏列表，支持不同版本返回不同类型的List<T>
  ///
  /// - 代理版（kIsFranchisee==true）返回 List<GameTypeV2>
  /// - 普通版返回 List<GameType>
  ///
  /// 调用时需指定泛型类型，如：
  ///   await fetchGameList<GameTypeV2>() 或 await fetchGameList<GameType>()
  Future<List<GameTypeV2>> fetchGameList<T>() async {
    // 如果已经有一个未完成的 Future，则直接返回它
    if (_fetchListFuture != null) {
      return _fetchListFuture!;
    }

    try {
      _fetchListFuture = _fetchGameListV2();
      final result = await _fetchListFuture!;
      _fetchListFuture = null; // 清除缓存，允许后续调用重新请求
      return result;
    } catch (e) {
      _fetchListFuture = null; // 清除缓存，避免后续调用卡住
      // 打印错误日志，便于排查
      LogE("fetchGameList<$T> error: $e");
      rethrow; // 继续抛出异常
    }
  }

  Future<List<GameV2>> fetchFavList() async {
    final res = await ChannelApi.fetchFavGameList();
    favGameIdList = res.map((e) => e.id).toList();

    /// 最近列表匹配 已收藏游戏
    if (favGameIdList.isNotEmpty) {
      /// 最近
      for (final game in GameRecentlyUtil().gameList) {
        game.isSavour = favGameIdList.contains(game.id);
      }
    }
    return res;
  }

  Future<List<GameTypeV2>> _fetchGameListV2() async {
    final gameListResult = await ChannelApi.fetchGameListV2();

    for (GameTypeV2 gameType in gameListResult) {
      gameType.sectionHeight = GameHomeUtil.calculateSectionHeight(
        gameTypeCode: gameType.code,
        gameOrPlatform: gameType.isGame == 0, platformsLength: gameType.data.length, gamesLength: gameType.games.length
      );
    }

    gameList = List.of(gameListResult);

    try {
      final jsonString = json.encode(gameList.map((game) => game.toJson()).toList());
      FileCacheManager().saveCache(kHomeCacheKey, jsonString);
    } catch (e) {
      LogD("缓存HomeData失败: $e");
    }
    return gameListResult;
  }

  // Future<List<GameType>> _fetchGameList() async {
  //   // 获取游戏列表
  //   final gameListResult = await ChannelApi.fetchGameList();
  //   if (gameListResult.errorStr == null) {
  //     platformInfoMap = gameListResult.platformMap;
  //     List<GameType> newGames = List.from(gameListResult.game);
  //
  //     for (GameType gameType in newGames) {
  //       // if (gameType.name == "热门" && popularEntity != null) {
  //       //   gameType.data = popularEntity!.popularVenue.map((e) => GamePlatform.fromPopularVenue(e)).toList();
  //       // }
  //       if (gameType.code == 'YLCP' || gameType.code == 'CP') {
  //         final list = await fetchLotteryList();
  //         if (list.isNotEmpty) {
  //           gameType.data = list.map((e) => GamePlatform.fromLotteryGroup(e)).toList();
  //         }
  //       }
  //
  //       /// 计算模块高度
  //       gameType.sectionHeight =
  //           GameHomeUtil.calculateSectionHeight(itemsLength: gameType.data.length, gameTypeCode: gameType.code);
  //     }
  //     gameList = List.of(newGames);
  //
  //     try {
  //       final jsonString = json.encode(gameList.map((game) => game.toJson()).toList());
  //       FileCacheManager().saveCache(kHomeCacheKey, jsonString);
  //     } catch (e) {
  //       LogD("缓存HomeData失败: $e");
  //     }
  //     return newGames;
  //   }
  //
  //   return [];
  // }

  Future<List<LotteryGroup>> fetchLotteryList() async {
    final res = await LotteryApi.fetchHomeLotteryList();
    if (res.isNotEmpty) {
      lotteryGroupList = res;
    }
    return res;
  }

  bool isFetchGameLogin = false;

  /// 获取游戏登录地址
  fetchGameLoginData({required int gameId, required int platformId}) async {
    if (isFetchGameLogin) return;
    isFetchGameLogin = true;
    GSEasyLoading.showLoading();
    try {
      /// 弹窗队列暂停
      DialogQueueManager().pauseQueue();
      // 获取游戏登录地址
      final result = await ChannelApi.fetchGameLoginUrl(gameId: gameId, thirdPlatformId: platformId);
      if (result != null) {
        switch (result.type) {
          case 1:
            await GameUtil.openGameWebView(url: result.content, platformId: platformId);
            break;
          case 2:
            await GameUtil.openGameHtmlView(content: result.content, platformId: platformId);
            break;
          default:
            break;
        }
        /// 弹窗队列继续
        DialogQueueManager().resumeQueue();
        sl<UserCubit>().fetchUserBalance();
      }
    } catch (_) {
      /// 弹窗队列继续
      DialogQueueManager().resumeQueue();
    } finally {
      isFetchGameLogin = false;
      GSEasyLoading.dismiss();
    }

  }

  static openGameWebView({required String url, required int platformId}) async {
    final flag = await sl<NavigatorService>().push(AppRouter.gameWebView, arguments: {'url': url});
    GameUtil.transferOutFromLoginPlatform(platformIds: [platformId]);
    return flag;
  }

  static openGameHtmlView({required String content, required int platformId}) async {
    final flag =  await sl<NavigatorService>().push(AppRouter.commonHtmlView, arguments: {'content': content});
    GameUtil.transferOutFromLoginPlatform(platformIds: [platformId]);
    return flag;
  }

  /// 转出已登录三方平台余额
  static transferOutFromLoginPlatform({List<int>? platformIds}) async {
    if (!sl<UserCubit>().state.isLogin) return;
    var tmpList = platformIds ?? List<int>.from(sl<UserCubit>().loggedInPlatformIds);
    for (int e in tmpList) {
      if (!sl<UserCubit>().state.isLogin) break;
      double amount = await ChannelApi.getPlatformAmount(platformId: e) ?? 0;
      if (amount > 0) {
        bool flag = await ChannelApi.transferGameAmount(AmountTransferType.out, amount: amount, platformId: e);
        if (flag) {
          sl<UserCubit>().removePlatformId(e);
        }
      }
    }
    await sl<UserCubit>().fetchUserBalance();
  }

  /// 转出所有三方平台余额
  static transferOutAllPlatform() async {
    final list = await ChannelApi.getAllPlatformAmount();
    List<int> existAmountPlatformIds = [];
    for (PlatformAmountEntity model in list) {
      if (model.amount > 0) {
        existAmountPlatformIds.add(model.platformId);
      }
    }
    if (existAmountPlatformIds.isNotEmpty) {
      await GameUtil.transferOutFromLoginPlatform(platformIds: existAmountPlatformIds);
    }
    await sl<UserCubit>().fetchUserBalance();
  }

  /// 查找彩票信息
  Future<({LotteryGroup? group, Lottery? lottery})> _findLotteryInfo(
    GamePlatformV2? platform,
    String gameCode,
  ) async {
    // 获取彩票列表
    List<LotteryGroup> lotteryGroupList = GameUtil().lotteryGroupList;
    if (lotteryGroupList.isEmpty) {
      lotteryGroupList = await GameUtil().fetchLotteryList();
    }

    // 如果列表为空，直接返回空结果
    if (lotteryGroupList.isEmpty) {
      return (group: null, lottery: null);
    }

    // TODO
    // 从指定平台查找
    if (platform != null) {
      // final group = platform.extraData as LotteryGroup;
      // final lottery = group.list.firstWhereOrNull(
      //   (lottery) => lottery.lotteryCode == gameCode,
      // );
      // return (group: group, lottery: lottery);
    }

    // 从所有平台查找
    for (final group in lotteryGroupList) {
      final lottery = group.list.firstWhereOrNull(
        (lottery) => lottery.lotteryCode == gameCode,
      );
      if (lottery != null) {
        return (group: group, lottery: lottery);
      }
    }

    return (group: null, lottery: null);
  }

  /// 处理彩票跳转
  Future<void> _handleLotteryNavigation(Lottery lottery, LotteryGroup group) async {
    // 记录点击
    await LotteryApi.submitClickLottery(id: lottery.id, groupId: group.id);

    // 特殊彩票处理
    if (lottery.lotteryCode == 'DWYDH') {
      await sl<NavigatorService>().push(AppRouter.animalGame, arguments: {'gameId': lottery.id});
      return;
    }

    // 普通彩票处理
    await sl<NavigatorService>().push(
      AppRouter.lotteryDetail,
      arguments: {
        'list': GameUtil().lotteryGroupList,
        'model': lottery,
      },
    );
  }

  /// 处理彩票游戏点击
  Future<void> handleLotteryGame({GamePlatformV2? platform, required GameV2 game}) async {
    try {
      final result = await _findLotteryInfo(platform, game.code);

      if (result.lottery != null && result.group != null) {
        await _handleLotteryNavigation(result.lottery!, result.group!);
      } else {
        GSEasyLoading.showToast("数据异常,请重试");
      }
    } catch (e) {
      LogE("处理彩票游戏出错: $e");
      GSEasyLoading.showToast("操作失败,请重试");
    }
  }

  bool isHandleGameCellClick = false;

  onClickGameCell({required GameV2 game, GamePlatformV2? platform}) async {
    if (isHandleGameCellClick) return;
    isHandleGameCellClick = true;
    try {
      if (game.type == 2) {
        await handleLotteryGame(platform: platform, game: game);
      } else {
        await fetchGameLoginData(gameId: game.id, platformId: game.platformId);
      }
      GameRecentlyUtil().addRecentGame(game);
    } finally {
      isHandleGameCellClick = false;
    }
  }


  /// 点击游戏收藏
  Future<bool> onClickGameFav({required bool isFav, required GameV2 game}) async {
    LogD("onClickGameFav>>>isFav: $isFav");
    final flag = await ChannelApi.operateGameFav(isFav: isFav, gameId: game.id, gameType: game.type);
    if (flag) {
      if (isFav) {
        favGameIdList.add(game.id);
      } else {
        favGameIdList.remove(game.id);
      }
    }
    return flag;
  }
}
