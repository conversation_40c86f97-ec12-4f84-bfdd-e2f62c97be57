import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/constants/constants.dart';


SystemConfigEntity $SystemConfigEntityFromJson(Map<String, dynamic> json) {
  final SystemConfigEntity systemConfigEntity = SystemConfigEntity();
  final String? inviteDomain = jsonConvert.convert<String>(json['invite_domain']);
  if (inviteDomain != null) {
    systemConfigEntity.inviteDomain = inviteDomain;
  }
  final String? loginCaptcha = jsonConvert.convert<String>(json['login_captcha']);
  if (loginCaptcha != null) {
    systemConfigEntity.loginCaptcha = loginCaptcha;
  }
  final String? registerCaptcha = jsonConvert.convert<String>(json['register_captcha']);
  if (registerCaptcha != null) {
    systemConfigEntity.registerCaptcha = registerCaptcha;
  }
  final String? chatDomain = jsonConvert.convert<String>(json['chat_domain']);
  if (chatDomain != null) {
    systemConfigEntity.chatDomain = chatDomain;
  }
  final String? videoDomain = jsonConvert.convert<String>(json['video_domain']);
  if (videoDomain != null) {
    systemConfigEntity.videoDomain = videoDomain;
  }
  final List<CurrencyConfig>? currencyList = (json['currency'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CurrencyConfig>(e) as CurrencyConfig).toList();
  if (currencyList != null) {
    systemConfigEntity.currencyList = currencyList;
  }
  final String? serviceUrl = jsonConvert.convert<String>(json['service_url']);
  if (serviceUrl != null) {
    systemConfigEntity.serviceUrl = serviceUrl;
  }
  final String? appDownloadUrl = jsonConvert.convert<String>(json['app_download_url']);
  if (appDownloadUrl != null) {
    systemConfigEntity.appDownloadUrl = appDownloadUrl;
  }
  final String? serviceOpenType = jsonConvert.convert<String>(json['service_open_type']);
  if (serviceOpenType != null) {
    systemConfigEntity.serviceOpenType = serviceOpenType;
  }
  final String? videoWatchLimit = jsonConvert.convert<String>(json['video_watch_limit']);
  if (videoWatchLimit != null) {
    systemConfigEntity.videoWatchLimit = videoWatchLimit;
  }
  final String? gamePicBaseUrl = jsonConvert.convert<String>(json['aws_access_domain']);
  if (gamePicBaseUrl != null) {
    systemConfigEntity.gamePicBaseUrl = gamePicBaseUrl;
  }
  final String? currencyCode = jsonConvert.convert<String>(json['currency_code']);
  if (currencyCode != null) {
    systemConfigEntity.currencyCode = currencyCode;
  }
  final String? currencySymbol = jsonConvert.convert<String>(json['currency_symbol']);
  if (currencySymbol != null) {
    systemConfigEntity.currencySymbol = currencySymbol;
  }
  final DialogIntervalSetting? dialogSetting = jsonConvert.convert<DialogIntervalSetting>(json['notice_interval_set']);
  if (dialogSetting != null) {
    systemConfigEntity.dialogSetting = dialogSetting;
  }
  final String? wtdCheckPhone = jsonConvert.convert<String>(json['wtdCheckPhone']);
  if (wtdCheckPhone != null) {
    systemConfigEntity.wtdCheckPhone = wtdCheckPhone;
  }
  final LanguageType? languageType = jsonConvert.convert<LanguageType>(json['languageType']);
  if (languageType != null) {
    systemConfigEntity.languageType = languageType;
  }
  final LoadLoginAndRegWay? loadLoginAndRegWay = jsonConvert.convert<LoadLoginAndRegWay>(json['loadLoginAndRegWay']);
  if (loadLoginAndRegWay != null) {
    systemConfigEntity.loadLoginAndRegWay = loadLoginAndRegWay;
  }
  return systemConfigEntity;
}

Map<String, dynamic> $SystemConfigEntityToJson(SystemConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['invite_domain'] = entity.inviteDomain;
  data['login_captcha'] = entity.loginCaptcha;
  data['register_captcha'] = entity.registerCaptcha;
  data['chat_domain'] = entity.chatDomain;
  data['video_domain'] = entity.videoDomain;
  data['currency'] = entity.currencyList.map((v) => v.toJson()).toList();
  data['service_url'] = entity.serviceUrl;
  data['app_download_url'] = entity.appDownloadUrl;
  data['service_open_type'] = entity.serviceOpenType;
  data['video_watch_limit'] = entity.videoWatchLimit;
  data['aws_access_domain'] = entity.gamePicBaseUrl;
  data['currency_code'] = entity.currencyCode;
  data['currency_symbol'] = entity.currencySymbol;
  data['notice_interval_set'] = entity.dialogSetting?.toJson();
  data['wtdCheckPhone'] = entity.wtdCheckPhone;
  data['languageType'] = entity.languageType.toJson();
  data['loadLoginAndRegWay'] = entity.loadLoginAndRegWay.toJson();
  return data;
}

extension SystemConfigEntityExtension on SystemConfigEntity {
  SystemConfigEntity copyWith({
    String? inviteDomain,
    String? loginCaptcha,
    String? registerCaptcha,
    String? chatDomain,
    String? videoDomain,
    List<CurrencyConfig>? currencyList,
    String? serviceUrl,
    String? appDownloadUrl,
    String? serviceOpenType,
    String? videoWatchLimit,
    String? gamePicBaseUrl,
    String? currencyCode,
    String? currencySymbol,
    DialogIntervalSetting? dialogSetting,
    String? wtdCheckPhone,
    LanguageType? languageType,
    LoadLoginAndRegWay? loadLoginAndRegWay,
  }) {
    return SystemConfigEntity()
      ..inviteDomain = inviteDomain ?? this.inviteDomain
      ..loginCaptcha = loginCaptcha ?? this.loginCaptcha
      ..registerCaptcha = registerCaptcha ?? this.registerCaptcha
      ..chatDomain = chatDomain ?? this.chatDomain
      ..videoDomain = videoDomain ?? this.videoDomain
      ..currencyList = currencyList ?? this.currencyList
      ..serviceUrl = serviceUrl ?? this.serviceUrl
      ..appDownloadUrl = appDownloadUrl ?? this.appDownloadUrl
      ..serviceOpenType = serviceOpenType ?? this.serviceOpenType
      ..videoWatchLimit = videoWatchLimit ?? this.videoWatchLimit
      ..gamePicBaseUrl = gamePicBaseUrl ?? this.gamePicBaseUrl
      ..currencyCode = currencyCode ?? this.currencyCode
      ..currencySymbol = currencySymbol ?? this.currencySymbol
      ..dialogSetting = dialogSetting ?? this.dialogSetting
      ..wtdCheckPhone = wtdCheckPhone ?? this.wtdCheckPhone
      ..languageType = languageType ?? this.languageType
      ..loadLoginAndRegWay = loadLoginAndRegWay ?? this.loadLoginAndRegWay;
  }
}

PictureConfig $PictureConfigFromJson(Map<String, dynamic> json) {
  final PictureConfig pictureConfig = PictureConfig();
  final String? videoWatchRule = jsonConvert.convert<String>(json['video_watch_rule']);
  if (videoWatchRule != null) {
    pictureConfig.videoWatchRule = videoWatchRule;
  }
  final String? agentPromotionUrl = jsonConvert.convert<String>(json['agent_promotion_url']);
  if (agentPromotionUrl != null) {
    pictureConfig.agentPromotionUrl = agentPromotionUrl;
  }
  return pictureConfig;
}

Map<String, dynamic> $PictureConfigToJson(PictureConfig entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['video_watch_rule'] = entity.videoWatchRule;
  data['agent_promotion_url'] = entity.agentPromotionUrl;
  return data;
}

extension PictureConfigExtension on PictureConfig {
  PictureConfig copyWith({
    String? videoWatchRule,
    String? agentPromotionUrl,
  }) {
    return PictureConfig()
      ..videoWatchRule = videoWatchRule ?? this.videoWatchRule
      ..agentPromotionUrl = agentPromotionUrl ?? this.agentPromotionUrl;
  }
}

LoadLoginAndRegWay $LoadLoginAndRegWayFromJson(Map<String, dynamic> json) {
  final LoadLoginAndRegWay loadLoginAndRegWay = LoadLoginAndRegWay();
  final String? login = jsonConvert.convert<String>(json['login']);
  if (login != null) {
    loadLoginAndRegWay.login = login;
  }
  final String? register = jsonConvert.convert<String>(json['register']);
  if (register != null) {
    loadLoginAndRegWay.register = register;
  }
  final String? defLogin = jsonConvert.convert<String>(json['defLogin']);
  if (defLogin != null) {
    loadLoginAndRegWay.defLogin = defLogin;
  }
  final String? defRegister = jsonConvert.convert<String>(json['defRegister']);
  if (defRegister != null) {
    loadLoginAndRegWay.defRegister = defRegister;
  }
  return loadLoginAndRegWay;
}

Map<String, dynamic> $LoadLoginAndRegWayToJson(LoadLoginAndRegWay entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['login'] = entity.login;
  data['register'] = entity.register;
  data['defLogin'] = entity.defLogin;
  data['defRegister'] = entity.defRegister;
  return data;
}

extension LoadLoginAndRegWayExtension on LoadLoginAndRegWay {
  LoadLoginAndRegWay copyWith({
    String? login,
    String? register,
    String? defLogin,
    String? defRegister,
  }) {
    return LoadLoginAndRegWay()
      ..login = login ?? this.login
      ..register = register ?? this.register
      ..defLogin = defLogin ?? this.defLogin
      ..defRegister = defRegister ?? this.defRegister;
  }
}

LanguageType $LanguageTypeFromJson(Map<String, dynamic> json) {
  final LanguageType languageType = LanguageType();
  final String? defaultLanguage = jsonConvert.convert<String>(json['defaultLanguage']);
  if (defaultLanguage != null) {
    languageType.defaultLanguage = defaultLanguage;
  }
  final List<LanguageConfig>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<LanguageConfig>(e) as LanguageConfig).toList();
  if (list != null) {
    languageType.list = list;
  }
  return languageType;
}

Map<String, dynamic> $LanguageTypeToJson(LanguageType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['defaultLanguage'] = entity.defaultLanguage;
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension LanguageTypeExtension on LanguageType {
  LanguageType copyWith({
    String? defaultLanguage,
    List<LanguageConfig>? list,
  }) {
    return LanguageType()
      ..defaultLanguage = defaultLanguage ?? this.defaultLanguage
      ..list = list ?? this.list;
  }
}

CurrencyConfig $CurrencyConfigFromJson(Map<String, dynamic> json) {
  final CurrencyConfig currencyConfig = CurrencyConfig();
  final String? id = jsonConvert.convert<String>(json['currencyId']);
  if (id != null) {
    currencyConfig.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['currencyName']);
  if (name != null) {
    currencyConfig.name = name;
  }
  final int? isDefault = jsonConvert.convert<int>(json['isDefault']);
  if (isDefault != null) {
    currencyConfig.isDefault = isDefault;
  }
  return currencyConfig;
}

Map<String, dynamic> $CurrencyConfigToJson(CurrencyConfig entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currencyId'] = entity.id;
  data['currencyName'] = entity.name;
  data['isDefault'] = entity.isDefault;
  return data;
}

extension CurrencyConfigExtension on CurrencyConfig {
  CurrencyConfig copyWith({
    String? id,
    String? name,
    int? isDefault,
  }) {
    return CurrencyConfig()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..isDefault = isDefault ?? this.isDefault;
  }
}

LanguageConfig $LanguageConfigFromJson(Map<String, dynamic> json) {
  final LanguageConfig languageConfig = LanguageConfig();
  final String? dictLabel = jsonConvert.convert<String>(json['dictLabel']);
  if (dictLabel != null) {
    languageConfig.dictLabel = dictLabel;
  }
  final String? dictValue = jsonConvert.convert<String>(json['dictValue']);
  if (dictValue != null) {
    languageConfig.dictValue = dictValue;
  }
  final int? dictSort = jsonConvert.convert<int>(json['dictSort']);
  if (dictSort != null) {
    languageConfig.dictSort = dictSort;
  }
  return languageConfig;
}

Map<String, dynamic> $LanguageConfigToJson(LanguageConfig entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dictLabel'] = entity.dictLabel;
  data['dictValue'] = entity.dictValue;
  data['dictSort'] = entity.dictSort;
  return data;
}

extension LanguageConfigExtension on LanguageConfig {
  LanguageConfig copyWith({
    String? dictLabel,
    String? dictValue,
    int? dictSort,
  }) {
    return LanguageConfig()
      ..dictLabel = dictLabel ?? this.dictLabel
      ..dictValue = dictValue ?? this.dictValue
      ..dictSort = dictSort ?? this.dictSort;
  }
}

DialogIntervalSetting $DialogIntervalSettingFromJson(Map<String, dynamic> json) {
  final DialogIntervalSetting dialogIntervalSetting = DialogIntervalSetting();
  final DialogInterval? luckyGift = jsonConvert.convert<DialogInterval>(json['luckyGift']);
  if (luckyGift != null) {
    dialogIntervalSetting.luckyGift = luckyGift;
  }
  final DialogInterval? notice = jsonConvert.convert<DialogInterval>(json['notice']);
  if (notice != null) {
    dialogIntervalSetting.notice = notice;
  }
  final DialogInterval? popular = jsonConvert.convert<DialogInterval>(json['popular']);
  if (popular != null) {
    dialogIntervalSetting.popular = popular;
  }
  return dialogIntervalSetting;
}

Map<String, dynamic> $DialogIntervalSettingToJson(DialogIntervalSetting entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['luckyGift'] = entity.luckyGift?.toJson();
  data['notice'] = entity.notice?.toJson();
  data['popular'] = entity.popular?.toJson();
  return data;
}

extension DialogIntervalSettingExtension on DialogIntervalSetting {
  DialogIntervalSetting copyWith({
    DialogInterval? luckyGift,
    DialogInterval? notice,
    DialogInterval? popular,
  }) {
    return DialogIntervalSetting()
      ..luckyGift = luckyGift ?? this.luckyGift
      ..notice = notice ?? this.notice
      ..popular = popular ?? this.popular;
  }
}

DialogInterval $DialogIntervalFromJson(Map<String, dynamic> json) {
  final DialogInterval dialogInterval = DialogInterval();
  final int? interval = jsonConvert.convert<int>(json['interval']);
  if (interval != null) {
    dialogInterval.interval = interval;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    dialogInterval.sort = sort;
  }
  return dialogInterval;
}

Map<String, dynamic> $DialogIntervalToJson(DialogInterval entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['interval'] = entity.interval;
  data['sort'] = entity.sort;
  return data;
}

extension DialogIntervalExtension on DialogInterval {
  DialogInterval copyWith({
    int? interval,
    int? sort,
  }) {
    return DialogInterval()
      ..interval = interval ?? this.interval
      ..sort = sort ?? this.sort;
  }
}