import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class CommonProgressBar extends StatefulWidget {
  /// 当前进度 (0.0 - 1.0)
  final double progress;

  /// 进度条高度
  final double height;

  /// 是否显示气泡提示
  final bool showTooltip;

  /// 气泡提示文本
  final String? tooltipText;

  /// 进度条背景色
  final Color backgroundColor;

  /// 进度条填充色
  final Color progressColor;

  /// 气泡背景色
  final Color tooltipColor;

  /// 左右内边距
  final double horizontalPadding;

  const CommonProgressBar({
    super.key,
    required this.progress,
    this.height = 8.0,
    this.showTooltip = true,
    this.tooltipText,
    this.backgroundColor = const Color(0xFF3B300E),
    this.progressColor = const Color(0xFFFFD038),
    this.tooltipColor = const Color(0xFF9F9F9F),
    this.horizontalPadding = 16.0,
  });

  @override
  State<CommonProgressBar> createState() => _CommonProgressBarState();
}

class _CommonProgressBarState extends State<CommonProgressBar> with SingleTickerProviderStateMixin {
  late final String tooltipText;
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    tooltipText = widget.tooltipText ?? "almost_there".tr();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // 立即启动动画，不延时
    WidgetsBinding.instance.addPostFrameCallback((_)  async {
      await Future.delayed(const Duration(milliseconds: 300));
      _animationController.forward();
    });
  }

  @override
  void didUpdateWidget(CommonProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));
      _animationController.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return LayoutBuilder(
          builder: (context, constraints) {
            final availableWidth = constraints.maxWidth - (widget.horizontalPadding * 2).gw;
            final progressWidth = availableWidth * _progressAnimation.value.clamp(0.0, 1.0);

            return Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: widget.horizontalPadding.gw),
              height: widget.height.gw + (widget.showTooltip ? 30.gw : 0), // 增加气泡预留空间，防止裁切
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // 进度条背景
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: widget.height.gw,
                      decoration: BoxDecoration(
                        color: widget.backgroundColor,
                        borderRadius: BorderRadius.circular((widget.height / 2).gw),
                      ),
                    ),
                  ),
                  // 进度条填充
                  Positioned(
                    bottom: 0,
                    left: 0,
                    child: Container(
                      height: widget.height.gw,
                      width: progressWidth,
                      decoration: BoxDecoration(
                        color: widget.progressColor,
                        borderRadius: BorderRadius.circular((widget.height / 2).gw),
                      ),
                    ),
                  ),
                  // 气泡提示
                  if (widget.showTooltip && _progressAnimation.value > 0.01)
                    Positioned(
                      left: _calculateTooltipPosition(progressWidth, availableWidth),
                      top: -2.gw, // 稍微向上移动一点
                      child: _buildTooltip(progressWidth, availableWidth),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // 计算气泡宽度
  double _calculateTooltipWidth() {
    // 使用 TextPainter 计算文本宽度
    final textSpan = TextSpan(
      text: tooltipText,
      style: TextStyle(color: context.colorTheme.btnBgTertiary).fs12,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: Directionality.of(context), // 使用当前上下文的文本方向
    );
    textPainter.layout();

    // 文本宽度 + 左右内边距 + 圆角
    return textPainter.width + 12.gw + 8.gw; // 12.gw 是左右内边距，8.gw 是圆角补偿
  }

  // 计算气泡位置，处理边界情况
  double _calculateTooltipPosition(double progressWidth, double availableWidth) {
    final tooltipWidth = _calculateTooltipWidth();
    final arrowOffset = tooltipWidth / 2; // 箭头在气泡中央

    // 理想位置：进度条右端 - 箭头偏移
    double idealPosition = progressWidth - arrowOffset;

    // 处理左边界
    if (idealPosition < 0) {
      return -16.gw;
    }

    // 处理右边界
    if (idealPosition + tooltipWidth > availableWidth) {
      return availableWidth - tooltipWidth;
    }

    return idealPosition;
  }

  // 计算箭头在气泡中的偏移，确保箭头始终指向进度条
  double _calculateArrowOffset(double progressWidth, double availableWidth) {
    final tooltipWidth = _calculateTooltipWidth();

    // 计算气泡的实际位置
    double tooltipPosition = _calculateTooltipPosition(progressWidth, availableWidth);

    // 箭头应该始终指向进度条的实际右端位置
    // 计算箭头相对于气泡左边缘的位置
    double arrowPosition = progressWidth - tooltipPosition;

    // 当进度条填充很少时，箭头应该指向进度条的实际位置
    // 不要强制限制到气泡边界，让箭头指向真实位置
    if (progressWidth < 6.0) {
      return progressWidth; // 直接指向进度条位置
    }

    // 确保箭头在气泡范围内，并且指向进度条右端
    return arrowPosition.clamp(6.0, tooltipWidth - 6.0);
  }

  Widget _buildTooltip(double progressWidth, double availableWidth) {
    final arrowOffset = _calculateArrowOffset(progressWidth, availableWidth);

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // 气泡尾巴 - 指向下方，位置动态调整
        Positioned(
          bottom: -5.gw,
          left: arrowOffset, // 箭头宽度的一半，确保箭头尖端指向正确位置
          child: Container(
            width: 0,
            height: 0,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: widget.tooltipColor,
                  width: 6.gw,
                ),
                left: BorderSide(
                  color: Colors.transparent,
                  width: 6.gw,
                ),
                right: BorderSide(
                  color: Colors.transparent,
                  width: 6.gw,
                ),
              ),
            ),
          ),
        ),
        // 气泡主体
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 0),
          decoration: BoxDecoration(
            color: widget.tooltipColor,
            borderRadius: BorderRadius.circular(4.gw),
          ),
          child: AneText(
            tooltipText,
            style: TextStyle(color: context.colorTheme.btnBgTertiary).fs12,
          ),
        ),
      ],
    );
  }
}
