import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class LuckyCountdownWidget extends StatefulWidget {
  final int timeDown;
  final VoidCallback? onTimeUp;

  const LuckyCountdownWidget({
    super.key, 
    required this.timeDown,
    this.onTimeUp,
  });

  @override
  State<StatefulWidget> createState() => _LuckyCountdownWidgetState();
}

class _LuckyCountdownWidgetState extends State<LuckyCountdownWidget> {
  late Timer _timer;
  int _remainingTime = 0;

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.timeDown;
    _startCountdown();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        timer.cancel();
        // 倒计时结束时调用回调
        widget.onTimeUp?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 计算天数、小时、分钟、秒数
    final days = _remainingTime ~/ 86400; // 86400 = 24 * 60 * 60
    final hours = (_remainingTime % 86400) ~/ 3600;
    final minutes = (_remainingTime % 3600) ~/ 60;
    final seconds = _remainingTime % 60;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (days > 0) ...[
          AneText(
            "$days ",
            style: context.textTheme.secondary,
          ),
          AneText(
            days > 1 ? 'days'.tr() : 'day'.tr(),
            style: context.textTheme.secondary,
          ),
          SizedBox(width: 8.gw),
        ],
        _buildTimeUnit(hours.toString().padLeft(2, '0')),
        _buildTimeSeparator(),
        _buildTimeUnit(minutes.toString().padLeft(2, '0')),
        _buildTimeSeparator(),
        _buildTimeUnit(seconds.toString().padLeft(2, '0')),
      ],
    );
  }

  Widget _buildTimeUnit(String time) {
    return Container(
      height: 20.gw,
      constraints: BoxConstraints(
        minWidth: 20.gw,
      ),
      padding: EdgeInsets.symmetric(horizontal: 4.gw),
      decoration: BoxDecoration(
        color: context.colorTheme.btnBgPrimary,
        borderRadius: BorderRadius.circular(4.gw),
      ),
      alignment: Alignment.center,
      child: AneText(
        time,
        style: context.textTheme.primary.w600.fs12.copyWith(color: context.colorTheme.btnBgTertiary),
      ),
    );
  }

  Widget _buildTimeSeparator() {
    return AneText(
      ' : ',
      style: context.textTheme.secondary.w600.fs12,
    );
  }
}
