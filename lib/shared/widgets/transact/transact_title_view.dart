import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../core/constants/assets.dart';

enum TransactTitleViewType {
  depositAmount("deposit_amount", "assets/images/transact/v3/icon_recharge_deposit.svg"),
  withdrawAmount("withdraw_amount", "assets/images/transact/v3/icon_withdraw_address.svg"),
  paymentMethod("payment_methods", "assets/images/transact/v3/icon_payment_method.svg"),
  selectChannel("select_channel", "assets/images/transact/v3/icon_recharge_channel.svg"),
  selectAddress("select_receiving_address", "assets/images/transact/v3/icon_withdraw_address.svg"),
  selectActivity("select_value_package", "assets/images/transact/v3/icon_recharge_activity.svg");

  final String path;
  final String name;

  const TransactTitleViewType(this.name, this.path);
}

class TransactTitleView extends StatelessWidget {
  final TransactTitleViewType type;
  final String? subTitle;
  final Widget? child;
  final VoidCallback? onManageTap;

  const TransactTitleView({
    super.key,
    required this.type,
    this.subTitle,
    this.child,
    this.onManageTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      height: 84.gw,
      color: context.colorTheme.foregroundColor,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildPrefixIcon(context),
          SizedBox(width: 12.gw),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              AneText(
                type.name.tr(),
                style: context.textTheme.secondary.fs20.w500,
              ),
              if (subTitle != null)
                AneText(
                  subTitle!,
                  style: context.textTheme.title,
                ),
            ],
          ),
          if (type == TransactTitleViewType.selectAddress) ...[
            const Spacer(),
            _buildSuffixIcon(context),
          ],
          if (child != null) Expanded(child: child!)
        ],
      ),
    );
  }

  Widget _buildPrefixIcon(BuildContext context) {
    return Container(
      width: 40.gw,
      height: 40.gw,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.gw),
        color: context.colorTheme.iconBgA,
      ),
      alignment: Alignment.center,
      child: SvgPicture.asset(type.path, height: 20.gw),
    );
  }

  Widget _buildSuffixIcon(BuildContext context) {
    return GestureDetector(
      onTap: onManageTap,
      child: Container(
        width: 40.gw,
        height: 40.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.gw),
          color: context.colorTheme.iconBgA,
        ),
        alignment: Alignment.center,
        child: SvgPicture.asset(Assets.iconBankManagement, height: 20.gw),
      ),
    );
  }
}
