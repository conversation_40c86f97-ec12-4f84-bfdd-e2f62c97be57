import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/withdraw_user_bank_list_entity.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/user/bank_ban_widget.dart';
import 'package:wd/shared/widgets/user/pay_card_cell.dart';

class WithdrawSelectBankCellViewModel {
  String bankName;
  String bankCode;
  String? realName;
  String cardNum;
  bool isBan; // 是否禁用

  WithdrawSelectBankCellViewModel({
    required this.bankName,
    required this.bankCode,
    this.realName,
    required this.cardNum,
    required this.isBan,
  });

  factory WithdrawSelectBankCellViewModel.formWithdrawUserBankBrief(WithdrawUserBankBrief model) {
    return WithdrawSelectBankCellViewModel(
        bankName: model.bankName,
        bankCode: model.bankCode,
        realName: model.realName,
        cardNum: model.cardNo,
        isBan: model.useStatus == 1);
  }

  factory WithdrawSelectBankCellViewModel.formWithdrawUserBankInfoEntity(WithdrawUserBankInfoEntity model) {
    return WithdrawSelectBankCellViewModel(
        bankName: model.bankName, bankCode: model.bankCode, cardNum: model.cardNo, isBan: model.useStatus == 1);
  }
}

class WithdrawSelectBankCell extends StatelessWidget {
  final WithdrawType type;
  final WithdrawSelectBankCellViewModel model;
  final bool isSel;
  final bool showNext;

  const WithdrawSelectBankCell({
    super.key,
    required this.type,
    required this.model,
    this.isSel = false,
    this.showNext = false,
  });

  @override
  Widget build(BuildContext context) {
    var brandName = model.bankName;
    var bgColor = PayCardCell.getBankColor(brandName).color;
    var isSmall = PayCardCell.getBankColor(brandName).isSmall;
    var cardNum = model.cardNum;
    if (type == WithdrawType.bankCard) {
      cardNum = model.cardNum.formatCardNumber();
    }
    return type == WithdrawType.bankCard
        ? _bankCard(bgColor, context, cardNum, isSmall)
        : _manualChannel(bgColor, context, cardNum);
  }

  Container _bankCard(Color bgColor, BuildContext context, String cardNum, bool isSmall) {
    String cardBackground = _getCardBackground(model.bankName);

    return Container(
      height: 120.gw,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            blurRadius: 2,
            offset: const Offset(0, 2),
            color: Colors.white.withOpacity(0.2),
          )
        ],
        borderRadius: BorderRadius.circular(18.gw),
        image: DecorationImage(
          image: AssetImage(cardBackground),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Card content
          Positioned.fill(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 16.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Bank logo
                      SizedBox(
                        height: 22.gw,
                        child: Row(
                          children: [
                            AppImage(
                              imageUrl: getBankImageStr(model.bankCode),
                              height: 18.gw,
                              width: 18.gw,
                              radius: 12.gw,
                              placeholder: Image.asset("assets/images/transact/icon_card.png"),
                              errorWidget: Image.asset("assets/images/transact/icon_card.png"),
                            ),
                            SizedBox(width: 10.gw),
                            Text(model.bankName,
                                overflow: TextOverflow.ellipsis, // 添加文本溢出处理
                                maxLines: 1,
                                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                      fontSize: 17.fs,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    )),
                          ],
                        ),
                      ),
                      // Copy icon or selection indicator
                      if (showNext)
                        Container(
                          width: 24.gw,
                          height: 24.gw,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFD038),
                            borderRadius: BorderRadius.circular(12.gw),
                          ),
                          child: Icon(
                            Icons.content_copy,
                            color: Colors.black,
                            size: 12.gw,
                          ),
                        ),
                      if (isSel)
                        SvgPicture.asset(
                          Assets.iconCheckmarkCircleSelected,
                          width: 24.0,
                          height: 24.0,
                        ),
                    ],
                  ),
                  const Spacer(),
                  // Card number
                  AutoSizeText(
                    cardNum,
                    style: TextStyle(
                      color: const Color(0xFFE8E8E8),
                      fontSize: 20.fs,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2.0,
                      fontFamily: 'DINCond-Bold',
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ],
                    ),
                    maxLines: 1,
                    minFontSize: 12.fs,
                  ),
                  SizedBox(height: 12.gw),
                  // Cardholder info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AutoSizeText(
                            'Card holder name',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 9.fs,
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 1,
                            minFontSize: 7.fs,
                          ),
                          SizedBox(height: 2.gw),
                          AutoSizeText(
                            model.realName ?? '',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.16),
                                ),
                              ],
                            ),
                            maxLines: 1,
                            minFontSize: 8.fs,
                          ),
                        ],
                      ),
                      if (model.isBan)
                        Padding(
                          padding: EdgeInsets.only(left: 8.gw),
                          child: const BankBanWidget(),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Chip element
          Positioned(
            right: 25.gw,
            bottom: 15.gw,
            child: Container(
              width: 35.gw,
              height: 27.gw,
              decoration: const BoxDecoration(
                image: DecorationImage(image: AssetImage(Assets.cardChip), fit: BoxFit.cover),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Container _manualChannel(Color bgColor, BuildContext context, String cardNum) {
    String cardBackground = _getCryptoCardBackground(model.bankName);

    return Container(
      height: 120.gw,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18.gw),
        image: DecorationImage(
          image: AssetImage(cardBackground),
          fit: BoxFit.cover,
        ),
        boxShadow: [
          BoxShadow(
            blurRadius: 2,
            offset: const Offset(0, 2),
            color: Colors.white.withOpacity(0.2),
          )
        ],
      ),
      child: Stack(
        children: [
          // Card content
          Positioned.fill(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 16.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Crypto logo and name
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              width: 28.gw,
                              height: 28.gw,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(14.gw),
                              ),
                              child: AppImage(
                                imageUrl: getBankImageStr(model.bankCode),
                                width: 24.gw,
                                height: 24.gw,
                                placeholder: Image.asset("assets/images/transact/icon_payment_1.png"),
                                errorWidget: Image.asset("assets/images/transact/icon_payment_1.png"),
                              ),
                            ),
                            SizedBox(width: 8.gw),
                            Expanded(
                              child: AutoSizeText(
                                model.bankName,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.fs,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                minFontSize: 8.fs,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Copy icon or selection indicator
                      if (showNext)
                        Container(
                          width: 24.gw,
                          height: 24.gw,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFD038),
                            borderRadius: BorderRadius.circular(12.gw),
                          ),
                          child: Icon(
                            Icons.content_copy,
                            color: Colors.black,
                            size: 12.gw,
                          ),
                        ),
                      if (isSel)
                        SvgPicture.asset(
                          Assets.iconCheckmarkCircleSelected,
                          width: 24.0,
                          height: 24.0,
                        ),
                      if (model.isBan)
                        Padding(
                          padding: EdgeInsets.only(left: 8.gw),
                          child: const BankBanWidget(),
                        ),
                    ],
                  ),
                  const Spacer(),
                  // Crypto address
                  AutoSizeText(
                    cardNum,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.fs,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.0,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ],
                    ),
                    maxLines: 2,
                    minFontSize: 10.fs,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Get card background based on bank name
  String _getCardBackground(String bankName) {
    final bankKeywords = {
      Assets.cardBG1: ["工商", "中国银行", "招商"],
      Assets.cardBG2: ["建设", "交通", "浦发"],
      Assets.cardBG3: ["农业", "民生", "邮储"],
      Assets.cardBG4: ["华夏", "中信", "广发"],
      Assets.cardBG5: ["上海", "兴业", "平安"],
    };

    for (var entry in bankKeywords.entries) {
      for (var keyword in entry.value) {
        if (bankName.contains(keyword)) {
          return entry.key;
        }
      }
    }

    // Default background
    final backgrounds = [Assets.cardBG1, Assets.cardBG2, Assets.cardBG3, Assets.cardBG4, Assets.cardBG5];
    return backgrounds[bankName.hashCode.abs() % backgrounds.length];
  }

  // Get crypto card background based on crypto type
  String _getCryptoCardBackground(String bankName) {
    if (bankName.toUpperCase().contains('USDT') || bankName.toUpperCase().contains('TRC20')) {
      return Assets.cardBG1;
    } else if (bankName.toUpperCase().contains('BNB') || bankName.toUpperCase().contains('BEP')) {
      return Assets.cardBG2;
    } else if (bankName.toUpperCase().contains('ETH') || bankName.toUpperCase().contains('ERC20')) {
      return Assets.cardBG3;
    } else if (bankName.toUpperCase().contains('BTC') || bankName.toUpperCase().contains('BITCOIN')) {
      return Assets.cardBG4;
    } else {
      return Assets.cardBG5; // Default crypto background
    }
  }
}
