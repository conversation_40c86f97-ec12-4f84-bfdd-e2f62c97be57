import 'package:flutter/material.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/withdraw_user_bank_list_entity.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/user/pay_card_cell.dart';

class WithdrawSelectBankCellViewModel {
  String bankName;
  String bankCode;
  String cardNum;
  bool isBan; // 是否禁用

  WithdrawSelectBankCellViewModel({
    required this.bankName,
    required this.bankCode,
    required this.cardNum,
    required this.isBan,
  });

  factory WithdrawSelectBankCellViewModel.formWithdrawUserBankBrief(WithdrawUserBankBrief model) {
    return WithdrawSelectBankCellViewModel(
        bankName: model.bankName, bankCode: model.bankCode, cardNum: model.cardNo, isBan: model.useStatus == 1);
  }

  factory WithdrawSelectBankCellViewModel.formWithdrawUserBankInfoEntity(WithdrawUserBankInfoEntity model) {
    return WithdrawSelectBankCellViewModel(
        bankName: model.bankName, bankCode: model.bankCode, cardNum: model.cardNo, isBan: model.useStatus == 1);
  }
}

class WithdrawSelectBankCell extends StatelessWidget {
  final WithdrawType type;
  final WithdrawSelectBankCellViewModel model;
  final bool isSel;
  final bool showNext;

  const WithdrawSelectBankCell({
    super.key,
    required this.type,
    required this.model,
    this.isSel = false,
    this.showNext = false,
  });

  @override
  Widget build(BuildContext context) {
    var brandName = model.bankName;
    var bgColor = PayCardCell.getBankColor(brandName).color;
    var isSmall = PayCardCell.getBankColor(brandName).isSmall;
    var cardNum = model.cardNum;
    if (type == WithdrawType.bankCard) {
      cardNum = model.cardNum.formatCardNumber();
    }
    return type == WithdrawType.bankCard
        ? _bankCard(bgColor, context, cardNum, isSmall)
        : _manualChannel(bgColor, context, cardNum);
  }

  Container _bankCard(Color bgColor, BuildContext context, String cardNum, bool isSmall) {
    return Container(
      height: 120.gw,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            blurRadius: 2,
            offset: const Offset(0, 2),
            color: Colors.white.withOpacity(0.2),
          )
        ],
        borderRadius: BorderRadius.circular(18.gw),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF114F99),
            Color(0xFF0025CE),
            Color(0xFF8000CE),
          ],
          stops: [0.0, 0.5, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // Noise texture overlay
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(18.gw),
              child: Image.asset(
                Assets.cardNoiseTexture,
                fit: BoxFit.cover,
                opacity: const AlwaysStoppedAnimation(0.3),
              ),
            ),
          ),
          // Card content
          Positioned.fill(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 16.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top row with bank logo and copy icon
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Bank logo (Visa style)
                      SizedBox(
                        height: 22.gw,
                        child: Row(
                          children: [
                            Container(
                              width: 18.gw,
                              height: 22.gw,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(2.gw),
                              ),
                            ),
                            SizedBox(width: 4.gw),
                            Text(
                              'VISA',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.fs,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.2,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Copy icon
                      if (showNext)
                        Container(
                          width: 24.gw,
                          height: 24.gw,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFD038),
                            borderRadius: BorderRadius.circular(12.gw),
                          ),
                          child: Icon(
                            Icons.content_copy,
                            color: Colors.black,
                            size: 12.gw,
                          ),
                        ),
                    ],
                  ),
                  const Spacer(),
                  // Card number
                  Text(
                    cardNum,
                    style: TextStyle(
                      color: const Color(0xFFE8E8E8),
                      fontSize: 20.fs,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2.0,
                      fontFamily: 'DINCond-Bold',
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12.gw),
                  // Bottom row with cardholder info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Card holder name',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 9.fs,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: 2.gw),
                          Text(
                            model.bankName,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expiry date',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 9.fs,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: 2.gw),
                          Text(
                            '02/30',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Chip element
          Positioned(
            right: 35.gw,
            bottom: 15.gw,
            child: Container(
              width: 35.gw,
              height: 27.gw,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.26),
                borderRadius: BorderRadius.circular(4.gw),
                border: Border.all(
                  color: Colors.white,
                  width: 0.8,
                ),
              ),
              child: CustomPaint(
                painter: ChipPainter(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Container _manualChannel(Color bgColor, BuildContext context, String cardNum) {
    // Determine gradient colors based on crypto type
    List<Color> gradientColors;
    if (model.bankName.toUpperCase().contains('USDT') || model.bankName.toUpperCase().contains('TRC20')) {
      gradientColors = [
        const Color(0xFF114F99),
        const Color(0xFF0025CE),
        const Color(0xFF8000CE),
      ];
    } else if (model.bankName.toUpperCase().contains('BNB') || model.bankName.toUpperCase().contains('BEP')) {
      gradientColors = [
        const Color(0xFFFFC632),
        const Color(0xFFF73A67),
        const Color(0xFFFD294F),
      ];
    } else {
      gradientColors = [
        const Color(0xFF114F99),
        const Color(0xFF0025CE),
        const Color(0xFF8000CE),
      ];
    }

    return Container(
      height: 120.gw,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18.gw),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
          stops: const [0.0, 0.5, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            blurRadius: 2,
            offset: const Offset(0, 2),
            color: Colors.white.withOpacity(0.2),
          )
        ],
      ),
      child: Stack(
        children: [
          // Noise texture overlay
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(18.gw),
              child: Image.asset(
                Assets.cardNoiseTexture,
                fit: BoxFit.cover,
                opacity: const AlwaysStoppedAnimation(0.3),
              ),
            ),
          ),
          // Card content
          Positioned.fill(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 16.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top row with crypto logo and copy icon
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Crypto logo and name
                      Row(
                        children: [
                          Container(
                            width: 28.gw,
                            height: 28.gw,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(14.gw),
                            ),
                            child: AppImage(
                              imageUrl: getBankImageStr(model.bankCode),
                              width: 24.gw,
                              height: 24.gw,
                              placeholder: Image.asset("assets/images/transact/icon_payment_1.png"),
                              errorWidget: Image.asset("assets/images/transact/icon_payment_1.png"),
                            ),
                          ),
                          SizedBox(width: 8.gw),
                          Text(
                            model.bankName.toUpperCase().contains('TRC20')
                                ? 'TRC20'
                                : model.bankName.toUpperCase().contains('BEP')
                                    ? 'BEP-20'
                                    : model.bankName,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      // Copy icon
                      if (showNext)
                        Container(
                          width: 24.gw,
                          height: 24.gw,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFD038),
                            borderRadius: BorderRadius.circular(12.gw),
                          ),
                          child: Icon(
                            Icons.content_copy,
                            color: Colors.black,
                            size: 12.gw,
                          ),
                        ),
                    ],
                  ),
                  const Spacer(),
                  // Crypto address
                  Text(
                    cardNum,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.fs,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.0,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for the chip element
class ChipPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 0.8
      ..style = PaintingStyle.stroke;

    // Draw chip lines
    final path = Path();

    // Vertical lines
    path.moveTo(size.width * 0.15, size.height * 0.3);
    path.lineTo(size.width * 0.15, size.height * 0.7);

    path.moveTo(size.width * 0.85, size.height * 0.1);
    path.lineTo(size.width * 0.85, size.height * 0.9);

    // Horizontal lines
    path.moveTo(size.width * 0.1, size.height * 0.6);
    path.lineTo(size.width * 0.9, size.height * 0.6);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
