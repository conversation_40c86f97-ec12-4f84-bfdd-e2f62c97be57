import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class TopUpActivityCell extends StatelessWidget {
  final String title;
  final String description;
  final bool isSelected;
  final bool disable;
  final GestureTapCallback? onTap;

  const TopUpActivityCell({
    super.key,
    required this.title,
    required this.description,
    required this.isSelected,
    required this.disable,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    Color? bgColor = isSelected ? const Color(0xff1c1a12) : context.colorTheme.highlightForeground;
    Border? borderColor = isSelected ? Border.all(color: context.theme.primaryColor, width: 0.5) : null;
    Color? titleColor = isSelected ? context.colorTheme.textPrimary : null;
    Color? desColor;

    if (disable) {
      bgColor = context.colorTheme.highlightForeground;
      titleColor = context.colorTheme.textSecondary.withOpacity(0.3);
      desColor = context.colorTheme.textTitle.withOpacity(0.31);
    }

    return InkWell(
      onTap: disable ? null : onTap,
      child: Container(
        padding: EdgeInsets.all(20.gw),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.gw),
          color: bgColor,
          border: borderColor,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AneText(
                    title,
                    style: context.textTheme.secondary.fs16.w500.copyWith(color: titleColor),
                  ),
                  if (description.isNotEmpty) ...[
                    SizedBox(height: 5.gw),
                    AneText(
                      description,
                      style: context.textTheme.title.fs16.w500.copyWith(color: desColor),
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(width: 10.gw),
            if (!disable)
              SvgPicture.asset(
                isSelected
                    ? "assets/images/checkmark/icon_checkmark_circle_selected.svg"
                    : "assets/images/checkmark/icon_checkmark_circle_unselected.svg",
                width: 24.gw,
                height: 24.gw,
              )
          ],
        ),
      ),
    );
  }
}
