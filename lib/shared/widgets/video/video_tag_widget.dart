import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

/// 视频标签组件
/// 用于显示视频的标签信息，如720P、时长等
class VideoTagWidget extends StatelessWidget {
  /// 标签文本
  final String title;
  
  /// 前缀图标路径（可选）
  final String? prefixIconPath;
  
  /// 背景透明度
  final double backgroundOpacity;
  
  /// 高度
  final double height;
  
  /// 水平内边距
  final double horizontalPadding;
  
  /// 图标大小
  final double iconSize;
  
  /// 图标和文本间距
  final double iconTextSpacing;

  const VideoTagWidget({
    super.key,
    required this.title,
    this.prefixIconPath,
    this.backgroundOpacity = 0.4,
    this.height = 20.0,
    this.horizontalPadding = 10.0,
    this.iconSize = 10.0,
    this.iconTextSpacing = 3.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height.gw,
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding.gw),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(backgroundOpacity),
        borderRadius: BorderRadius.all(Radius.circular(height.gw)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (prefixIconPath != null) ...[
            SvgPicture.asset(
              prefixIconPath!,
              width: iconSize.gw,
              height: iconSize.gw,
            ),
            SizedBox(width: iconTextSpacing.gw),
          ],
          AneText(
            title,
            style: context.textTheme.secondary.fs12,
          ),
        ],
      ),
    );
  }
}
