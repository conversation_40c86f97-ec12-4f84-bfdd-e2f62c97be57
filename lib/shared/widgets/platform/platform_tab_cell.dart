import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class PlatformTabCellViewModel {
  final String title;
  final bool isHot;
  final String activeIcon;
  final String? inactiveIcon;

  PlatformTabCellViewModel({
    required this.title,
    this.isHot = false,
    required this.activeIcon,
    this.inactiveIcon,
  });

  factory PlatformTabCellViewModel.fromGameTypeV2(GameTypeV2 gameType) {
    final baseImgUrl = GlobalConfig().systemConfig.gamePicBaseUrl;
    return PlatformTabCellViewModel(
      title: gameType.name,
      isHot: gameType.isPopular,
      activeIcon: baseImgUrl + gameType.classIcon.checked,
      inactiveIcon: baseImgUrl + gameType.classIcon.unchecked,
    );
  }

  factory PlatformTabCellViewModel.fromActivityGameTypeViewModel(ActivityGameTypeViewModel model) {
    return PlatformTabCellViewModel(
      title: model.categoryName,
      activeIcon: model.icon,
    );
  }

  factory PlatformTabCellViewModel.fromActivityTaskTypeViewModel(ActivityTaskTypeViewModel model) {
    return PlatformTabCellViewModel(
      title: model.categoryName,
      activeIcon: model.icon,
    );
  }
}

class PlatformTabCell extends StatelessWidget {
  final PlatformTabCellViewModel model;
  final bool isSel;

  const PlatformTabCell({
    super.key,
    required this.model,
    required this.isSel,
  });

  @override
  Widget build(BuildContext context) {
    String bgImagePath = "assets/images/home/<USER>/bg_home_platform_${isSel ? '' : 'un'}sel.png";

    return SizedBox(
      width: 60.gw,
      height: 69.gw,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          /// 背景图
          Positioned(
            bottom: 0,
            child: Image.asset(bgImagePath, width: 60.gw, height: 60.gw),
          ),
          AppImage(
            imageUrl: isSel ? model.activeIcon : (model.inactiveIcon ?? model.activeIcon),
            placeholder: const SizedBox.shrink(),
            errorWidget: const SizedBox.shrink(),
            width: 60.gw,
            height: 64.gw,
          ),

          Positioned(
              bottom: 2.gw,
              left: 0,
              right: 0,
              child: Center(
                  child: AneText(
                model.title,
                style: context.textTheme.secondary.copyWith(
                  color: isSel ? context.theme.primaryColor : null,
                ),
              ))),
          if (model.isHot)
            Positioned(
                top: 6.gw,
                left: -10.gw,
                child: Image.asset(
                  "assets/images/home/<USER>/icon_home_tab_kind_isHot.gif",
                  width: 40.gw,
                  height: 30.gw,
                ))
        ],
      ),
    );
  }
}
