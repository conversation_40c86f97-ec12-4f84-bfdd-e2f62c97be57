import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/statement_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/common_copy_button.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class GSStateListCellModel {
  String title;
  String orderNo;
  int dateStr;
  double amount; // 账变金额
  double beforeAmount; // 账变前金额
  double afterAmount; // 账变后金额

  GSStateListCellModel({
    required this.title,
    required this.orderNo,
    required this.dateStr,
    required this.amount,
    required this.beforeAmount,
    required this.afterAmount,
  });

  factory GSStateListCellModel.formStatementRecords(StatementRecords model) {
    return GSStateListCellModel(
        title: '${model.fundChangeWayName}-${model.fundChangeTypeName}',
        orderNo: model.transactionNo ?? '',
        dateStr: model.createTime,
        amount: model.changeAmount,
        beforeAmount: model.beforeAccountAmount,
        afterAmount: model.afterAccountAmount);
  }
}

class GSStateListCell extends StatelessWidget {
  final VoidCallback? onPressed;
  final GSStateListCellModel model;

  const GSStateListCell({super.key, required this.model, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return HeaderContentCard(
      margin: EdgeInsets.symmetric(horizontal: 12.gw),
      radius: 12,
      header: Text(
        model.title,
        style: context.textTheme.btnSecondary.fs16.w600,
      ),
      content: Column(
        children: [
          _buildRow(context, title: 'transactionTime'.tr(), data: TimeUtil.convertTimeStampToFull(model.dateStr)),
          _buildRow(context, title: 'orderId'.tr(), data: model.orderNo, showCopy: true),
          _buildRow(context, title: 'amount'.tr(), data: model.amount.formattedMoney),
          _buildRow(context, title: 'beforeAmount'.tr(), data: model.beforeAmount.formattedMoney),
          _buildRow(context, title: 'afterAmount'.tr(), data: model.afterAmount.formattedMoney, showDivider: false),
        ],
      ),
    );
  }

  _buildRow(
    BuildContext context, {
    required String title,
    required String data,
    bool showDivider = true,
    bool showCopy = false,
  }) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: showCopy ? 12.gw : 20.gw),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AneText(
                title,
                style: context.textTheme.title.fs15.w600.ffAne,
              ),
              const Spacer(),
              if (showCopy)
                CommonCopyButton(text: data)
              else
                AneText(
                  data,
                  style: context.textTheme.title.fs15.w600.ffAne,
                ),
            ],
          ),
        ),
        if (showDivider)
          Divider(
            color: Theme.of(context).dividerColor,
            height: 1,
          ),
      ],
    );
  }
}
