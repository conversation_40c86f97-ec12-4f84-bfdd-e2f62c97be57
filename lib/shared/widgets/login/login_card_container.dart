import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/theme/themes.dart';

import 'package:wd/core/utils/screenUtil.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

class LoginCardContainer extends StatefulWidget {
  final Widget child;
  final Widget? title;
  final Widget? subtitle;
  final String? backgroundImagePath;

  const LoginCardContainer({
    super.key,
    required this.child,
    this.title,
    this.subtitle,
    this.backgroundImagePath,
  });

  @override
  State<LoginCardContainer> createState() => _LoginCardContainerState();
}

class _LoginCardContainerState extends State<LoginCardContainer> {
  final ScrollController _scrollController = ScrollController();
  late KeyboardVisibilityController _keyboardVisibilityController;

  @override
  void initState() {
    super.initState();
    _keyboardVisibilityController = KeyboardVisibilityController();

    // 添加键盘监听
    _keyboardVisibilityController.onChange.listen((bool visible) {
      if (visible) {
        if (!kIsWeb) {
          Future.delayed(const Duration(milliseconds: 100), () {
            _handleScroll();
          });
        }
      } else {
        // 键盘隐藏时滚动回顶部
        _scrollToTop();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return KeyboardDismisser(
      child: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        extendBodyBehindAppBar: false,
        resizeToAvoidBottomInset: true,
        body: Focus(
          onFocusChange: (hasFocus) {
            if (kIsWeb) {
              if (hasFocus) {
                _handleWebScroll();
              } else {
                // Web端失去焦点时滚动回顶部
                _scrollToTop();
              }
            }
          },
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight + 100,
              ),
              child: Column(
                children: [
                  _buildHeaderSection(context),
                  _buildCardView(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleScroll() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        250.gw,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _handleWebScroll() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        150.gw,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildHeaderSection(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36; // ~400px on 1252px screen

    return Stack(
      children: [
        // Background image with gradient overlay
        Container(
          height: headerHeight,
          width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                widget.backgroundImagePath ?? 'assets/images/login/bg_login_logo.png',
              ),
              fit: BoxFit.cover,
            ),
          ),
          child: _buildLogoSection(context),
        ),
        // Back button
        _buildBackBtn(context),
      ],
    );
  }

  Widget _buildLogoSection(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        90.verticalSpace,
        // WD Logo
        SvgPicture.asset(
          Assets.tabLogo,
          width: 131.gw,
          height: 60.gw,
        ),
        20.verticalSpace,
        // Title
        if (widget.title != null) widget.title!,
        8.verticalSpace,
        // Subtitle
        if (widget.subtitle != null) widget.subtitle!,
      ],
    );
  }

  Widget _buildBackBtn(BuildContext context) {
    return Positioned(
      top: ScreenUtil().statusBarHeight + 10.gw,
      left: 15.gw,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          alignment: Alignment.center,
          child: Image.asset(
            Assets.iconBack,
            height: 32.gw,
            width: 32.gw,
          ),
        ),
      ),
    );
  }

  Widget _buildCardView(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final cardHeight = screenHeight * 0.64; // ~600px+ on 1252px screen

    return Container(
      height: cardHeight,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 12.gw, bottom: 24.gw),
        child: widget.child,
      ),
    );
  }
}
