import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/models/entities/video_hot_movies_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/shared/widgets/tiktok/home_movie_cell_v2.dart';

import '../../../core/models/entities/video_hot_tag_entity.dart';
import '../../../features/page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter_cubit.dart';
import '../../../features/routers/app_router.dart';
import '../../../features/routers/navigator_utils.dart';
import '../../../injection_container.dart';

class VideoSliverMovieGridView extends StatelessWidget {
  final double paddingH;
  final VideoHotMoviesEntity? videoList;
  final List<VideoHotMovies>? filterVideoList;
  final ValueChanged<VideoHotMovies> onTapCell;
  final bool isGrouped;
  final bool hideCategory;
  final int animationKey;

  const VideoSliverMovieGridView({
    super.key,
    this.paddingH = 0,
    this.videoList,
    this.filterVideoList,
    required this.onTapCell,
    this.animationKey = 0,
    this.isGrouped = true,
    this.hideCategory = false,
  });

  @override
  Widget build(BuildContext context) {
    final gridSpacing = 11.gw;
    final gridItemWidth = (GSScreenUtil().screenWidth - paddingH * 2 - gridSpacing * 2) / 3;
    final gridItemImageHeight = 155.gw;
    final gridItemHeight = gridItemImageHeight + 65.gw;
    final gridItemRatio = gridItemWidth / gridItemHeight;
    final cubit = sl<PopularVideoFilterCubit>();
    final category = cubit.state.categoryFilter;
    final categoryKey = cubit.state.videoHotTags?.moviesCategory
        ?.firstWhere((e) => e.dictValue == category, orElse: () => VideoHotTagMoviesCategory())
        .dictKey;
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final category = isGrouped ? videoList?.list?.keys.elementAt(index) : categoryKey;
          final movies = isGrouped ? (videoList?.list?[category]?.take(6).toList() ?? []) : (filterVideoList ?? []);


          if (movies.isEmpty) {
            return Container(
                height: 400.gw,
                alignment: Alignment.center,
                child: const EmptyWidget());
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!hideCategory)
                _CategoryHeader(
                  paddingH: paddingH,
                  category: category,
                ),
              AnimationLimiter(
                child: GridView.builder(
                  padding: EdgeInsets.symmetric(horizontal: paddingH),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: gridSpacing,
                    mainAxisSpacing: gridSpacing,
                    childAspectRatio: gridItemRatio,
                  ),
                  itemCount: movies.length,
                  itemBuilder: (context, index) {
                    final model = movies[index];
                    return AnimationConfiguration.staggeredGrid(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      columnCount: 3,
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: InkWell(
                            onTap: () => onTapCell(model),
                            child: HomeMovieCell(model: model),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
        childCount: isGrouped ? videoList?.list?.length ?? 0 : 1,
      ),
    );
  }
}

class _CategoryHeader extends StatelessWidget {
  const _CategoryHeader({
    required this.paddingH,
    required this.category,
  });

  final double paddingH;
  final String? category;

  String _getCategoryTitle(String? category) {
    if (category == null) return '';

    final videoHotTags = sl<PopularVideoFilterCubit>().state.videoHotTags;
    if (videoHotTags == null) return '';

    final tag = videoHotTags.moviesCategory?.firstWhere(
      (element) => element.dictKey == category,
      orElse: () => VideoHotTagMoviesCategory(),
    );

    return tag?.dictValue ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final categoryTitle = _getCategoryTitle(category);

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: paddingH,
        vertical: 10.gw,
      ),
      child: Row(
        children: [
          Image.asset("assets/images/video/icon_popular_title_tag.png", width: 20.gw, height: 20.gw),
          SizedBox(width: 8.gw),
          AneText(
            categoryTitle,
            style: context.textTheme.secondary.fs20.w500,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              sl<PopularVideoFilterCubit>().onSelectCategoryFilter(item: categoryTitle);
              sl<NavigatorService>().push(
                AppRouter.popularVideoFilter,
                arguments: {'category': category},
              );
            },
            child: Row(
              children: [
                AneText(
                  'all'.tr(),
                  style: context.textTheme.secondary,
                ),
                SizedBox(width: 5.gw),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12.gw,
                  color: context.colorTheme.textSecondary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
