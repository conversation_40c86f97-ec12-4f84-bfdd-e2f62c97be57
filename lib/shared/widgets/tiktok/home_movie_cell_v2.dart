import 'package:flutter/material.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/models/entities/video_hot_movies_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/shared/widgets/video/video_tag_widget.dart';

class HomeMovieCell extends StatelessWidget {
  final VideoHotMovies model;

  const HomeMovieCell({
    super.key,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片
        Expanded(child: _getImageContainer(context)),
        SizedBox(height: 8.gw),
        // 标题
        Container(
          height: 33.gw,
          alignment: Alignment.centerLeft,
          child: AneText(
            model.videoTitle ?? '',
            overflow: TextOverflow.ellipsis,
            style: context.textTheme.secondary.fs16,
            maxLines: 1,
          ),
        ),
      ],
    );
  }

  _getImageContainer(context) {
    return Stack(
      children: [
        Positioned(
          left: 4.gw,
          right: 4.gw,
          top: 4.gw,
          bottom: 4.gw,
          child: AppImage(
            fit: BoxFit.cover,
            imageUrl: model.videoImage ?? '',
            radius: 10.gw,
            placeholder: Container(color: const Color(0xffD9D9D9)),
          ),
        ),
        // 热门
        Positioned(
            left: 0,
            top: 0,
            child: Image.asset(
              "assets/images/tiktok/icon_video_new.png",
              width: 51.gw,
              height: 40.gw,
            )),
        // 清晰度
        Positioned(right: 6.gw, top: 6.gw, child: VideoTagWidget(title: model.videoClarity)),
        // 时长
        Positioned(
          right: 6.gw,
          bottom: 6.gw,
          child: VideoTagWidget(
            title: model.videoTime,
            prefixIconPath: "assets/images/video/icon_duration.svg",
          ),
        ),
      ],
    );
  }

}
