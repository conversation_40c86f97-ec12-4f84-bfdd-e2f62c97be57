import 'package:flutter/material.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/color_filtered/gray_filtered_widget.dart';
import 'package:wd/shared/widgets/user/bank_ban_widget.dart';

import '../../../core/models/apis/transact.dart';

class PayCardCell extends StatelessWidget {
  final PaymentCardEntity model;

  const PayCardCell(this.model, {super.key});

  @override
  Widget build(BuildContext context) {
    var brandName = model.bankName;
    var cardNo = model.cardNo;
    if (model.type == WithdrawType.bankCard.index + 1) {
      cardNo = model.cardNo.formatCardNumber();
    }
    return GrayFilteredWidget(
      showGrayFiltered: model.useStatus == 1,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              blurRadius: 2,
              offset: const Offset(0, 2),
              color: Colors.white.withOpacity(0.2),
            )
          ],
          borderRadius: BorderRadius.circular(18.gw),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF114F99),
              Color(0xFF0025CE),
              Color(0xFF8000CE),
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // Noise texture overlay
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(18.gw),
                child: Image.asset(
                  Assets.cardNoiseTexture,
                  fit: BoxFit.cover,
                  opacity: const AlwaysStoppedAnimation(0.3),
                ),
              ),
            ),
            // Card content
            Container(
              height: 120.gw,
              width: double.infinity,
              padding: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 16.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top row with bank logo
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Bank logo (Visa style)
                      SizedBox(
                        height: 22.gw,
                        child: Row(
                          children: [
                            Container(
                              width: 18.gw,
                              height: 22.gw,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(2.gw),
                              ),
                            ),
                            SizedBox(width: 4.gw),
                            Text(
                              'VISA',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.fs,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.2,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (model.useStatus == 1) const BankBanWidget(),
                    ],
                  ),
                  const Spacer(),
                  // Card number
                  Text(
                    cardNo,
                    style: TextStyle(
                      color: const Color(0xFFE8E8E8),
                      fontSize: 18.fs,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2.0,
                      fontFamily: 'DINCond-Bold',
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 8.gw),
                  // Bottom row with cardholder info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Card holder name',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 9.fs,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: 2.gw),
                          Text(
                            brandName,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expiry date',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 9.fs,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: 2.gw),
                          Text(
                            '02/30',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Chip element
            Positioned(
              right: 35.gw,
              bottom: 15.gw,
              child: Container(
                width: 35.gw,
                height: 27.gw,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.26),
                  borderRadius: BorderRadius.circular(4.gw),
                  border: Border.all(
                    color: Colors.white,
                    width: 0.8,
                  ),
                ),
                child: CustomPaint(
                  painter: ChipPainter(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static ({Color color, bool isSmall}) getBankColor(String bankName) {
    // 定义银行关键字与对应颜色的映射
    final Map<Color, List<String>> bankColorMap = {
      const Color(0xffE04851): ["工商", "中国银行", "招商", "华夏", "中信", "广发", "北京"],
      const Color(0xff2E6BB2): ["建设", "交通", "浦发", "上海", "兴业"],
      const Color(0xff0BA37E): ["农业", "民生", "邮储"],
      const Color(0xffda632c): ["平安"],
    };

    // 遍历映射，查找匹配的颜色
    for (var entry in bankColorMap.entries) {
      for (var keyword in entry.value) {
        if (bankName.contains(keyword)) {
          return (color: entry.key, isSmall: false);
        }
      }
    }

    // 如果没有匹配到，返回默认颜色
    return (color: const Color(0xffCDB296), isSmall: true); // 默认色;
  }
}

// Custom painter for the chip element
class ChipPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 0.8
      ..style = PaintingStyle.stroke;

    // Draw chip lines
    final path = Path();

    // Vertical lines
    path.moveTo(size.width * 0.15, size.height * 0.3);
    path.lineTo(size.width * 0.15, size.height * 0.7);

    path.moveTo(size.width * 0.85, size.height * 0.1);
    path.lineTo(size.width * 0.85, size.height * 0.9);

    // Horizontal lines
    path.moveTo(size.width * 0.1, size.height * 0.6);
    path.lineTo(size.width * 0.9, size.height * 0.6);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
