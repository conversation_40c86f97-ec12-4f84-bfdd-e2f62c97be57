import 'package:flutter/material.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/color_filtered/gray_filtered_widget.dart';
import 'package:wd/shared/widgets/user/bank_ban_widget.dart';

import '../../../core/models/apis/transact.dart';

class PayCardCell extends StatelessWidget {
  final PaymentCardEntity model;

  const PayCardCell(this.model, {super.key});

  @override
  Widget build(BuildContext context) {
    var brandName = model.bankName;
    Color bgColor = PayCardCell.getBankColor(brandName).color;
    var cardNo = model.cardNo;
    if (model.type == WithdrawType.bankCard.index + 1) {
      cardNo = model.cardNo.formatCardNumber();
    }
    return GrayFilteredWidget(
      showGrayFiltered: model.useStatus == 1,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              blurRadius: 2,
              offset: const Offset(0, 2),
              color: Colors.white.withOpacity(0.2),
            )
          ],
          borderRadius: BorderRadius.circular(10.gw), // 使用和之前相同的圆角
          color: bgColor, // 背景色
        ),
        child: Container(
          height: 120.gw,
          width: double.infinity,
          padding: EdgeInsets.fromLTRB(12.5.gw, 12.gw, 20.gw, 18.gw),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    height: 30.gw,
                    width: 30.gw,
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(15.gw)),
                    alignment: Alignment.center,
                    child: AppImage(
                      imageUrl: getBankImageStr(model.bankCode),
                      // 待修改
                      height: 24.gw,
                      width: 24.gw,
                      radius: 12.gw,
                      placeholder: Image.asset("assets/images/common/icon_bank_placeholder.png"),
                      errorWidget: Image.asset("assets/images/common/icon_bank_placeholder.png"),
                    ),
                  ),
                  SizedBox(width: 10.gw),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(model.bankName,
                          style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                fontSize: 17.fs,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              )),
                      if (model.branchBankAddress.isNotEmpty) ...[
                        SizedBox(height: 4.gw),
                        Text(model.branchBankAddress,
                            style: Theme.of(context)
                                .textTheme
                                .displayLarge
                                ?.copyWith(fontSize: 12.fs, color: Colors.white.withOpacity(0.8))),
                      ],
                    ],
                  ),
                  if (model.useStatus == 1) ...[
                    const Spacer(),
                    const BankBanWidget(),
                  ],
                ],
              ),
              Row(
                children: [
                  SizedBox(width: 40.gw),
                  Expanded(
                    child: Text(cardNo,
                        style: Theme.of(context).textTheme.displayLarge?.copyWith(
                              fontFamily: model.type == 1 ? 'WeChatSansStd' : null,
                              fontSize: 20.fs,
                              color: Colors.white,
                            )),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  static ({Color color, bool isSmall}) getBankColor(String bankName) {
    // 定义银行关键字与对应颜色的映射
    final Map<Color, List<String>> bankColorMap = {
      const Color(0xffE04851): ["工商", "中国银行", "招商", "华夏", "中信", "广发", "北京"],
      const Color(0xff2E6BB2): ["建设", "交通", "浦发", "上海", "兴业"],
      const Color(0xff0BA37E): ["农业", "民生", "邮储"],
      const Color(0xffda632c): ["平安"],
    };

    // 遍历映射，查找匹配的颜色
    for (var entry in bankColorMap.entries) {
      for (var keyword in entry.value) {
        if (bankName.contains(keyword)) {
          return (color: entry.key, isSmall: false);
        }
      }
    }

    // 如果没有匹配到，返回默认颜色
    return (color: const Color(0xffCDB296), isSmall: true); // 默认色;
  }
}
