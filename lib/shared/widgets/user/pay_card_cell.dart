import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/color_filtered/gray_filtered_widget.dart';
import 'package:wd/shared/widgets/user/bank_ban_widget.dart';

import '../../../core/models/apis/transact.dart';

class PayCardCell extends StatelessWidget {
  final PaymentCardEntity model;

  const PayCardCell(this.model, {super.key});

  @override
  Widget build(BuildContext context) {
    var brandName = model.bankName;
    var cardNo = model.cardNo;
    if (model.type == WithdrawType.bankCard.index + 1) {
      cardNo = model.cardNo.formatCardNumber();
    }
    return GrayFilteredWidget(
      showGrayFiltered: model.useStatus == 1,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              blurRadius: 2,
              offset: const Offset(0, 2),
              color: Colors.white.withOpacity(0.2),
            )
          ],
          borderRadius: BorderRadius.circular(18.gw),
          image: DecorationImage(
            image: AssetImage(_getCardBackground(brandName)),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            // Card content
            Container(
              height: 250.gw,
              width: double.infinity,
              padding: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 16.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top row with bank logo
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Bank logo
                      SizedBox(
                        height: 22.gw,
                        child: Row(
                          children: [
                            AppImage(
                              imageUrl: getBankImageStr(model.bankCode),
                              height: 18.gw,
                              width: 18.gw,
                              radius: 12.gw,
                              placeholder: Image.asset("assets/images/transact/icon_card.png"),
                              errorWidget: Image.asset("assets/images/transact/icon_card.png"),
                            ),
                            SizedBox(width: 10.gw),
                            Text(model.bankName,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                      fontSize: 17.fs,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    )),
                          ],
                        ),
                      ),
                      if (model.useStatus == 1) const BankBanWidget(),
                    ],
                  ),
                  const Spacer(),
                  // Card number
                  AutoSizeText(
                    cardNo,
                    style: TextStyle(
                      color: const Color(0xFFE8E8E8),
                      fontSize: 18.fs,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2.0,
                      fontFamily: 'DINCond-Bold',
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ],
                    ),
                    maxLines: 1,
                    minFontSize: 12.fs,
                  ),
                  SizedBox(height: 8.gw),
                  // Cardholder info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AutoSizeText(
                            'Card holder name',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 9.fs,
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 1,
                            minFontSize: 7.fs,
                          ),
                          SizedBox(height: 2.gw),
                          AutoSizeText(
                            brandName,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.fs,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.16),
                                ),
                              ],
                            ),
                            maxLines: 1,
                            minFontSize: 8.fs,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Chip element
            Positioned(
              right: 25.gw,
              bottom: 15.gw,
              child: Container(
                width: 35.gw,
                height: 27.gw,
                decoration: const BoxDecoration(
                  image: DecorationImage(image: AssetImage(Assets.cardChip), fit: BoxFit.cover),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static ({Color color, bool isSmall}) getBankColor(String bankName) {
    // 定义银行关键字与对应颜色的映射
    final Map<Color, List<String>> bankColorMap = {
      const Color(0xffE04851): ["工商", "中国银行", "招商", "华夏", "中信", "广发", "北京"],
      const Color(0xff2E6BB2): ["建设", "交通", "浦发", "上海", "兴业"],
      const Color(0xff0BA37E): ["农业", "民生", "邮储"],
      const Color(0xffda632c): ["平安"],
    };

    // 遍历映射，查找匹配的颜色
    for (var entry in bankColorMap.entries) {
      for (var keyword in entry.value) {
        if (bankName.contains(keyword)) {
          return (color: entry.key, isSmall: false);
        }
      }
    }

    // 如果没有匹配到，返回默认颜色
    return (color: const Color(0xffCDB296), isSmall: true); // 默认色;
  }

  // Helper method to get card background based on bank name
  String _getCardBackground(String bankName) {
    // Map different banks to different card backgrounds using all 5 designs
    final bankKeywords = {
      Assets.cardBG1: ["工商", "中国银行", "招商"],
      Assets.cardBG2: ["建设", "交通", "浦发"],
      Assets.cardBG3: ["农业", "民生", "邮储"],
      Assets.cardBG4: ["华夏", "中信", "广发"],
      Assets.cardBG5: ["上海", "兴业", "平安"],
    };

    for (var entry in bankKeywords.entries) {
      for (var keyword in entry.value) {
        if (bankName.contains(keyword)) {
          return entry.key;
        }
      }
    }

    // Default background - cycle through backgrounds based on hash
    final backgrounds = [Assets.cardBG1, Assets.cardBG2, Assets.cardBG3, Assets.cardBG4, Assets.cardBG5];
    return backgrounds[bankName.hashCode.abs() % backgrounds.length];
  }
}
