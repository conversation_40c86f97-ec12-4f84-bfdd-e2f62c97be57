import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/dialog/base/base_dialog_widget.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

/// 热门活动
class HotEventDialog {
  final List<HomeFeedItem> data;

  HotEventDialog({
    required this.data,
  });

  show() async {
    final context = sl<NavigatorService>().navigatorKey.currentContext;
    if (context != null && context.mounted) {
      return await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return _HotEventDialogContent(
            data: data,
          );
        },
      );
    }
  }
}

class _HotEventDialogContent extends StatefulWidget {
  final List<HomeFeedItem> data;

  const _HotEventDialogContent({
    required this.data,
  });

  @override
  State<_HotEventDialogContent> createState() => _HotEventDialogContentState();
}

class _HotEventDialogContentState extends State<_HotEventDialogContent> {
  @override
  Widget build(BuildContext context) {
    return BaseDialogWidget(
      children: [
        _buildTitle(context),
        SizedBox(height: 24.gw),
        _buildEventCards(),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'hot'.tr(), // 热门
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 5.gw),
        AneText(
          "events".tr(), // 活动
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  Widget _buildEventCards() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: min(widget.data.length, 4),
      separatorBuilder: (context, index) => SizedBox(height: 8.gw),
      itemBuilder: (context, index) {
        final item = widget.data[index];
        return _buildEventCard(
          title: item.noticeTitle,
          description: item.description,
          imageUrl: item.noticeContent,
          onTap: () {
            Navigator.of(context).pop();
            sl<NavigatorService>().push(AppRouter.activityDetail, arguments: {"id": item.activeId});
          },
        );
      },
    );
  }

  Widget _buildEventCard({
    required String title,
    required String description,
    required String imageUrl,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 129.gw,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.gw),
        ),
        child: Column(
          children: [
            // 内容
            Expanded(
                child: Stack(
              children: [
                Positioned.fill(
                  child: AppImage(imageUrl: imageUrl, fit: BoxFit.fill),
                ),
                Positioned(
                    top: 35.gw,
                    right: 48.gw,
                    child: Text(
                      title,
                      style: context.textTheme.secondary.fs28.w700,
                    ))
              ],
            )),
            Container(
              width: double.infinity,
              height: 29.gw,
              color: context.theme.primaryColor,
              alignment: Alignment.center,
              child: AneText("view_details".tr(),
                  style: TextStyle(color: context.theme.popupMenuTheme.color, fontSize: 14.gw).w500),
            )
          ],
        ),
      ),
    );
  }

}
