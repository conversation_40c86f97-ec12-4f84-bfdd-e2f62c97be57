import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class BaseDialogWidget extends StatefulWidget {
  final List<Widget> children;
  final EdgeInsets? padding;
  final bool enableAnimation; // 是否启用动画
  const BaseDialogWidget({
    super.key,
    required this.children,
    this.padding,
    this.enableAnimation = false, // 默认不启用动画
  });

  @override
  State<BaseDialogWidget> createState() => _BaseDialogWidgetState();
}

class _BaseDialogWidgetState extends State<BaseDialogWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    if (widget.enableAnimation) {
      _animationController = AnimationController(
        duration: const Duration(milliseconds: 1000),
        vsync: this,
      );

      // 从中间向四周放大的动画
      _scaleAnimation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ));

      // 启动动画
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    if (widget.enableAnimation) {
      _animationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final content = Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(
          width: 362.gw,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCloseButton(context),
              SizedBox(height: 12.gw),
              Container(
                padding: widget.padding ?? EdgeInsets.all(24.gw),
                decoration: BoxDecoration(
                  image:
                      const DecorationImage(image: AssetImage("assets/images/alert/bg_common.png"), fit: BoxFit.fill),
                  borderRadius: BorderRadius.circular(16.gw),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.children,
                ),
              ),
            ],
          ),
        ),
      ),
    );

    // 如果启用动画，包装动画效果
    if (widget.enableAnimation) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: content,
          );
        },
      );
    }

    return content;
  }

  Widget _buildCloseButton(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: 32.gw,
          height: 32.gw,
          decoration: BoxDecoration(
            color: context.colorTheme.textSecondary.withOpacity(0.1),
            border: Border.all(color: context.colorTheme.textTitle),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Icon(
            Icons.close,
            color: context.colorTheme.textSecondary,
            size: 16.gw,
          ),
        ),
      ),
    );
  }
}
