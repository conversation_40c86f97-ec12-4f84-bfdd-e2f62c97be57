import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/dialog/base/base_dialog_widget.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

/// 欢迎礼包
class WelcomeBonusDialog {
  final List<HomeFeedItem> data;

  WelcomeBonusDialog({
    required this.data,
  });

  show() async {
    final context = sl<NavigatorService>().navigatorKey.currentContext;
    if (context != null && context.mounted) {
      final list = _setupData();
      return await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return _WelcomeBonusDialogContent(
            data: list,
          );
        },
      );
    }
  }

  List<WelcomeActivityCellVM> _setupData() {
    final noWantVM = WelcomeActivityCellVM(
        imgUrl: "assets/images/activity/icon_welcome_dont_want.png",
        content: "no_welcome_bonus".tr(),
        isSel: false,
        isNoWant: true);

    return data
        .take(4)
        .map((e) => WelcomeActivityCellVM(
              imgUrl: e.noticeContent,
              title: e.noticeTitle,
              content: e.description,
              isSel: true,
            ))
        .toList()
      ..add(noWantVM);
  }
}

class _WelcomeBonusDialogContent extends StatefulWidget {
  final List<WelcomeActivityCellVM> data;

  const _WelcomeBonusDialogContent({
    required this.data,
  });

  @override
  State<_WelcomeBonusDialogContent> createState() => _WelcomeBonusDialogContentState();
}

class _WelcomeBonusDialogContentState extends State<_WelcomeBonusDialogContent> {
  @override
  Widget build(BuildContext context) {
    return BaseDialogWidget(
      children: [
        _buildTitle(context),
        SizedBox(height: 16.gw),
        _buildListViewWidget(),
        SizedBox(height: 10.gw),
        _buildLoginTips(context),
        SizedBox(height: 16.gw),
        CommonButton(
          title: 'choose'.tr(),
          enable: widget.data.firstWhereOrNull((e) => e.isSel) != null,
          onPressed: () {
            final needToRecharge = widget.data.firstWhereOrNull((e) => e.isNoWant)?.isSel == false;
            sl<NavigatorService>().pop();
            sl<NavigatorService>().push(
              AppRouter.register,
              arguments: {"pushRechargeAfterLogin": needToRecharge},
            );
          },
        ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'choose_your'.tr(), // 选择你的
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 5.gw),
        AneText(
          "welcome_bonus".tr(), // 欢迎礼包
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  _buildListViewWidget() {
    return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final model = widget.data[index];
          return WelcomeActivityCell(
              model: model,
              onTap: () {
                setState(() {
                  if (model.isNoWant) {
                    // 点击"不需要欢迎奖励"时
                    if (!model.isSel) {
                      // 如果当前未选中，则选中它并取消其他所有选项
                      widget.data.forEach((e) => e.isSel = false);
                      model.isSel = true;
                    } else {
                      // 如果当前已选中，则取消选中
                      model.isSel = false;
                    }
                  } else {
                    // 点击其他选项时
                    model.isSel = !model.isSel;
                    // 取消"不需要欢迎奖励"的选中状态
                    final noWantItem = widget.data.firstWhere((e) => e.isNoWant);
                    noWantItem.isSel = false;
                  }
                  setState(() {});
                });
              });
        },
        separatorBuilder: (_, __) => SizedBox(height: 8.gw),
        itemCount: widget.data.length);
  }

  Widget _buildLoginTips(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AneText(
          "already_have_account".tr(),
          style: context.textTheme.title.fs16,
        ),
        SizedBox(width: 3.gw),
        InkWell(
          onTap: () {
            sl<NavigatorService>().pop();
            sl<NavigatorService>().push(AppRouter.login);
          },
          child: AneText("sign_in".tr(),
              style: context.textTheme.primary.fs16.copyWith(
                decoration: TextDecoration.underline,
                // 可选：样式/颜色/粗细
                decorationStyle: TextDecorationStyle.solid, // solid | double | dotted | dashed | wavy
                decorationColor: context.colorTheme.textPrimary,
                decorationThickness: 1.5, // 粗细
              )),
        ),
      ],
    );
  }
}

class WelcomeActivityCellVM {
  final String imgUrl;
  final String? title;
  final String? content;
  bool isSel;
  final bool isNoWant;

  WelcomeActivityCellVM({
    required this.imgUrl,
    this.title,
    this.content,
    required this.isSel,
    this.isNoWant = false,
  });
}

class WelcomeActivityCell extends StatelessWidget {
  final WelcomeActivityCellVM model;
  final GestureTapCallback onTap;

  const WelcomeActivityCell({
    super.key,
    required this.model,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.foregroundColor,
          borderRadius: BorderRadius.circular(8.gw),
        ),
        child: Row(
          children: [
            AppImage(imageUrl: model.imgUrl, width: 53.gw, height: 53.gw),
            SizedBox(width: 16.gw),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (model.title != null) ...[
                    AneText(
                      model.title!,
                      style: context.textTheme.secondary.fs16.w600,
                      maxLines: 2,
                    ),
                  ],
                  if (model.title != null && model.content != null) ...[SizedBox(height: 5.gw)],
                  if (model.content != null) ...[
                    AneText(
                      model.content!,
                      style: context.textTheme.secondary.ffAne.opa80,
                      maxLines: 2,
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(width: 10.gw),
            SvgPicture.asset(
              model.isSel
                  ? "assets/images/checkmark/icon_checkmark_spot_selected.svg"
                  : "assets/images/checkmark/icon_checkmark_spot_unselected.svg",
              width: 14.gw,
              height: 14.gw,
            ),
          ],
        ),
      ),
    );
  }
}
