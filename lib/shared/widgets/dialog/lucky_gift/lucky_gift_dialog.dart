import 'package:flutter/material.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/models/apis/activity.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/utils/dialog_queue_manager/dialog_queue_manager.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/dialog/lucky_gift/lucky_gift_unpick_widget.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'lucky_gift_invite_widget.dart';
import 'lucky_gift_picked_widget.dart';

/// 幸运礼包
class LuckyGiftDialog {
  final HomeFeedLuckyGift model;

  LuckyGiftDialog({required this.model});

  show() async {
    /// 初始状态，未领取任务
    if (model.status == 0) {
      await _showUnpick(onTapSure: () => onClickSureAction());
    }

    /// 任务进行中
    else if (model.status >= 1) {
      await _showInviteFriend(onTapSure: () => onClickSureAction());
    }
  }

  void onClickSureAction() async {
    switch (model.status) {
      ///未领取任务
      case 0:
        {
          final res = await ActivityApi.claimLuckyGiftTask();
          if (res != null) {
            sl<NavigatorService>().pop();
            await LuckyGiftDialog(model: res).showPicked(onTapSure: () async {
              sl<NavigatorService>().pop();
              await LuckyGiftDialog(model: res).show();
            });
          }
        }
        break;

      /// 已领取任务-未完成
      case 1:
        {
          sl<NavigatorService>().pop();
          await Future.delayed(const Duration(milliseconds: 300));
          sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.agent);
        }
        break;

      /// 已领取任务-已完成未领取奖励
      case 2:
        {
          final flag = await ActivityApi.claimLuckyGiftBonus();
          if (flag) {
            GSEasyLoading.showToast("领取成功");
          }
        }
        break;
    }
  }

  _showUnpick({onTapSure}) {
    final context = sl<NavigatorService>().navigatorKey.currentContext;
    if (context != null && context.mounted) {
      return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return LuckyGiftUnpickWidget(
              winnerList: model.receiveUserList, remainingTime: model.expiredTime, onTapSure: onTapSure);
        },
      );
    }
  }

  /// 领取任务后（仅出现在点击领取任务按钮后）
  showPicked({onTapSure}) {
    final context = sl<NavigatorService>().navigatorKey.currentContext;
    if (context != null && context.mounted) {
      return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return LuckyGiftPickedWidget(
            rewardAmount: model.receiveMoney,
            targetAmount: model.bonusMoney,
            onTapSure: onTapSure,
          );
        },
      );
    }
  }

  _showInviteFriend({onTapSure}) {
    final context = sl<NavigatorService>().navigatorKey.currentContext;
    if (context != null && context.mounted) {
      return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return LuckyGiftInProgressWidget(
            currentAmount: model.receiveMoney,
            targetAmount: model.bonusMoney,
            claimedUsers: model.totalReceiveUser,
            totalClaimedAmount: model.totalReceiveMoney,
            remainingTime: model.expiredTime,
            status: LuckyGiftTaskStatus.fromValue(model.status),
            onStatusChanged: (status) => model.status = status.value,
            onTapSure: onTapSure,
          );
        },
      );
    }
  }
}
