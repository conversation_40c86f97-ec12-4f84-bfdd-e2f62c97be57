import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/countdown/lucky_countdown_widget.dart';
import 'package:wd/shared/widgets/dialog/base/base_dialog_widget.dart';
import 'package:wd/shared/widgets/progress_bar/common_progress_bar.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

enum LuckyGiftTaskStatus {
  unknown(99), // 未知
  inProgress(1), // 进行中
  completed(2), // 已完成未领取
  claimed(3), // 已完成已领取
  expired(4); // 已失效

  final int value;
  const LuckyGiftTaskStatus(this.value);
  static LuckyGiftTaskStatus fromValue(int value) {
    return LuckyGiftTaskStatus.values.firstWhere(
          (e) => e.value == value,
      orElse: () => unknown,
    );
  }
}

/// 幸运礼包-进行中，(邀请好友/领取奖励)
class LuckyGiftInProgressWidget extends StatefulWidget {
  final VoidCallback onTapSure;

  final LuckyGiftTaskStatus status;

  /// 当前金额
  final double currentAmount;

  /// 目标金额
  final double targetAmount;

  /// 已领取用户数
  final int claimedUsers;

  /// 总领取金额
  final double totalClaimedAmount;

  /// 倒计时总秒数
  final int remainingTime;

  /// 状态变化回调
  final Function(LuckyGiftTaskStatus)? onStatusChanged;

  const LuckyGiftInProgressWidget({
    super.key,
    required this.onTapSure,
    required this.status,
    required this.currentAmount,
    required this.targetAmount,
    required this.claimedUsers,
    required this.totalClaimedAmount,
    required this.remainingTime,
    this.onStatusChanged,
  });

  @override
  State<StatefulWidget> createState() => _LuckyGiftInProgressWidgetState();
}

class _LuckyGiftInProgressWidgetState extends State<LuckyGiftInProgressWidget> {
  late LuckyGiftTaskStatus _status;

  @override
  void initState() {
    _status = widget.status;
    super.initState();
  }

  @override
  void didUpdateWidget(LuckyGiftInProgressWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 外部状态更新
    if (oldWidget.status != widget.status) {
      setState(() {
        _status = widget.status;
      });
      // 通知外部状态变化
      widget.onStatusChanged?.call(_status);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseDialogWidget(
      enableAnimation: true, // 启用动画
      children: [
        _buildTitle(context),
        SizedBox(height: 5.gw),
        _buildUserStats(context),
        _buildImage(),
        _buildProgressSection(),
        SizedBox(height: 16.gw),
        _buildRemainingAmount(context),
        SizedBox(height: 24.gw),
        _buildInviteButton(context),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'lucky'.tr(), // 幸运
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 5.gw),
        AneText(
          "gift_pack".tr(), // 礼包
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  Widget _buildUserStats(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: [
          TextSpan(
            text:
                "${widget.claimedUsers.formattedMoney} ",
            style: context.textTheme.secondary.copyWith(
              color: context.colorTheme.btnBgPrimary,
            ).ffAne,
          ),
          TextSpan(
            text: "users_claimed".tr(), // 用户已领取
            style: context.textTheme.secondary.ffAne,
          ),
          TextSpan(
            text:
                " ${GlobalConfig().systemConfig.currencySymbol}${widget.totalClaimedAmount.formattedMoney} ",
            style: context.textTheme.secondary.ffAne,
          ),
          TextSpan(
            text: "total".tr(), // 总计
            style: context.textTheme.secondary.ffAne,
          ),
        ],
      ),
    );
  }

  Widget _buildImage() {
    return Image.asset("assets/images/activity/icon_lucky_pack_0.png", width: 216.gw, height: 182.gw);
  }

  Widget _buildProgressSection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.gw),
      child: Column(
        children: [
          _buildCountdown(context),
          _buildValueWidget(),
        ],
      ),
    );
  }

  Widget _buildCountdown(BuildContext context) {
    final int totalSeconds = max(widget.remainingTime, 0);

    return Container(
      height: 40.gw,
      color: context.colorTheme.foregroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AneText(
            "ends_in_days".tr(), // 还剩
            style: context.textTheme.secondary,
          ),
          LuckyCountdownWidget(
            timeDown: totalSeconds,
            onTimeUp: () {
              // 倒计时结束时更新状态为已过期
              setState(() {
                _status = LuckyGiftTaskStatus.expired;
              });
              // 通知外部状态变化
              widget.onStatusChanged?.call(_status);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildValueWidget() {
    return Container(
      width: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCurrentAmount(context),
          _buildProgressBar(context),
          SizedBox(height: 20.gw),
        ],
      ),
    );
  }

  Widget _buildCurrentAmount(BuildContext context) {
    return AneText(
      "${GlobalConfig().systemConfig.currencySymbol} ${widget.currentAmount.removeZeros}",
      style: context.textTheme.primary.w700.copyWith(
        fontSize: 36.gw,
        color: context.colorTheme.btnBgPrimary,
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context) {
    final progress = widget.currentAmount / widget.targetAmount;
    final remainingAmount = widget.targetAmount - widget.currentAmount;

    return CommonProgressBar(
      progress: progress.clamp(0.0, 1.0),
      showTooltip: remainingAmount > 0,
      tooltipText: "almost_there".tr(),
      backgroundColor: const Color(0xFF3B300E),
      progressColor: const Color(0xFFFFD038),
      tooltipColor: const Color(0xFF9F9F9F),
      horizontalPadding: 16.gw,
    );
  }

  Widget _buildRemainingAmount(BuildContext context) {
    final remainingAmount = widget.targetAmount - widget.currentAmount;

    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: [
          TextSpan(
            text: "almost_there_remaining".tr(), // 即将达成--还差
            style: context.textTheme.secondary.fs16,
          ),
          TextSpan(
            text: "${GlobalConfig().systemConfig.currencySymbol}${remainingAmount.removeZeros} ",
            style: context.textTheme.secondary.fs16.copyWith(
              color: context.colorTheme.btnBgPrimary,
            ),
          ),
          TextSpan(
            text: "to_go".tr(), // 即可领取
            style: context.textTheme.secondary.fs16,
          ),
        ],
      ),
    );
  }

  Widget _buildInviteButton(BuildContext context) {
    return CommonButton(
      title: _getBtnTitle(), // 邀请好友加速领取
      enable: _status != LuckyGiftTaskStatus.expired,
      onPressed: widget.onTapSure,
    );
  }

  String _getBtnTitle() {
    switch (widget.status) {
      case LuckyGiftTaskStatus.inProgress:
        return 'invite_friends_speed_up_claim'.tr(); // 邀请好友加速领取
      case LuckyGiftTaskStatus.completed:
        return 'claim_bonus'.tr(); // 领取奖励
      case LuckyGiftTaskStatus.claimed:
        return 'act_claimed'.tr(); // 已领取奖励
      case LuckyGiftTaskStatus.expired:
        return 'act_expired'.tr(); // 已过期
      default:
        return '';
    }
  }
}
