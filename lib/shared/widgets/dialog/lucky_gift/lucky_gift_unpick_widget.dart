import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/dialog/base/base_dialog_widget.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class LuckyGiftUnpickWidget extends StatefulWidget {
  /// 获奖名单
  final List<LuckyGiftReceiveUser> winnerList;

  /// 倒计时
  final int remainingTime;

  /// 按钮点击回调
  final VoidCallback onTapSure;

  const LuckyGiftUnpickWidget({
    super.key,
    required this.winnerList,
    required this.remainingTime,
    required this.onTapSure,
  });

  @override
  State<StatefulWidget> createState() => _LuckyGiftUnpickWidgetState();
}

class _LuckyGiftUnpickWidgetState extends State<LuckyGiftUnpickWidget> {
  late Timer _timer;
  late Timer _scrollTimer;
  late ScrollController _scrollController;
  int _remainingTime = 0;
  int _currentItemIndex = 0;

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.remainingTime;
    _scrollController = ScrollController();
    _startCountdown();
    _startScrollAnimation();
  }

  @override
  void dispose() {
    _timer.cancel();
    _scrollTimer.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _startScrollAnimation() {
    _scrollTimer = Timer.periodic(const Duration(milliseconds: 2000), (timer) {
      if (mounted && widget.winnerList.isNotEmpty) {
        _currentItemIndex = (_currentItemIndex + 1) % widget.winnerList.length;
        _scrollToNextItem();
      }
    });
  }

  void _scrollToNextItem() {
    if (_scrollController.hasClients) {
      final itemHeight = 42.gw;
      final targetOffset = _currentItemIndex * itemHeight;

      // 如果滚动到第二组数据的开始，重置到第一组
      if (_currentItemIndex >= widget.winnerList.length) {
        _scrollController.jumpTo(0);
        _currentItemIndex = 0;
      } else {
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  Widget _buildTimeUnit(String time) {
    return Container(
      height: 20.gw,
      constraints: BoxConstraints(
        minWidth: 20.gw,
      ),
      padding: EdgeInsets.symmetric(horizontal: 4.gw),
      decoration: BoxDecoration(
        color: context.colorTheme.btnBgPrimary,
        borderRadius: BorderRadius.circular(4.gw),
      ),
      alignment: Alignment.center,
      child: AneText(
        time,
        style: context.textTheme.primary.w600.fs12.copyWith(color: context.colorTheme.btnBgTertiary),
      ),
    );
  }

  Widget _buildTimeSeparator() {
    return AneText(
      ' : ',
      style: context.textTheme.secondary.w600.fs12,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseDialogWidget(
      children: [
        _buildTitle(context),
        SizedBox(height: 16.gw),
        _buildTimeDownWidget(context),
        SizedBox(height: 10.gw),
        _buildImage(),
        AneText(
          "claim_your_lucky_bonus".tr(),
          style: context.textTheme.secondary.fs16.w600,
        ),
        SizedBox(height: 16.gw),
        CommonButton(
          title: 'claim_lucky_gift_pack'.tr(), // 领取幸运礼包
          onPressed: widget.onTapSure,
        ),
        SizedBox(height: 16.gw),
        _buildWinnerListView(),
        SizedBox(height: 16.gw),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'lucky'.tr(), // 幸运
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 5.gw),
        AneText(
          "gift_pack".tr(), // 礼包
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  Widget _buildTimeDownWidget(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          "claim_countdown".tr(), // 领取倒计时
          style: context.textTheme.secondary,
        ),
        SizedBox(width: 8.gw),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildTimeUnit((_remainingTime ~/ 3600).toString().padLeft(2, '0')),
            _buildTimeSeparator(),
            _buildTimeUnit(((_remainingTime % 3600) ~/ 60).toString().padLeft(2, '0')),
            _buildTimeSeparator(),
            _buildTimeUnit((_remainingTime % 60).toString().padLeft(2, '0')),
          ],
        ),
      ],
    );
  }

  Widget _buildImage() {
    return Image.asset("assets/images/activity/icon_lucky_pack_0.png", width: 216.gw, height: 182.gw);
  }

  Widget _buildWinnerListView() {
    if (widget.winnerList.isEmpty) {
      return const SizedBox.shrink();
    }

    // 创建循环列表，复制原始数据以实现无缝滚动
    final extendedList = [...widget.winnerList, ...widget.winnerList];

    return Container(
      height: 126.gw,
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: extendedList.length,
        physics: const NeverScrollableScrollPhysics(), // 禁止手动滑动
        itemBuilder: (context, index) {
          final winner = extendedList[index];
          return Container(
            height: 42.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AneText(
                  winner.userNo,
                  style: context.textTheme.secondary.opa80,
                ),
                AneText("\$ ${winner.bonusMoney}", style: context.textTheme.secondary.fs16.w500),
              ],
            ),
          );
        },
      ),
    );
  }
}
