import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/dialog/base/base_dialog_widget.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class LuckyGiftPickedWidget extends StatefulWidget {
  /// 奖励金额
  final double rewardAmount;
  
  /// 目标金额
  final double targetAmount;

  /// 按钮点击回调
  final VoidCallback onTapSure;


  const LuckyGiftPickedWidget({
    super.key,
    required this.rewardAmount,
    required this.targetAmount,
    required this.onTapSure,
  });

  @override
  State<StatefulWidget> createState() => _LuckyGiftPickedWidgetState();
}

class _LuckyGiftPickedWidgetState extends State<LuckyGiftPickedWidget> {
  @override
  Widget build(BuildContext context) {
    return BaseDialogWidget(
        enableAnimation: true,
      children: [
        _buildTitle(context),
        SizedBox(height: 8.gw),
        _buildSubtitle(context),
        SizedBox(height: 24.gw),
        _buildImage(),
        SizedBox(height: 16.gw),
        _buildRewardAmount(context),
        SizedBox(height: 16.gw),
        _buildInstructions(context),
        SizedBox(height: 24.gw),
        _buildTaskButton(context),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return AneText(
      "congratulations".tr(), // 祝贺！
      style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return AneText(
      "you_received_free_reward".tr(), // 您已获得免费奖励
      style: context.textTheme.secondary.fs16,
    );
  }

  Widget _buildImage() {
    return Image.asset("assets/images/activity/icon_lucky_pack_1.png", width: 216.gw, height: 182.gw);
  }

  Widget _buildRewardAmount(BuildContext context) {
    return AneText(
      "${GlobalConfig().systemConfig.currencySymbol} ${widget.rewardAmount.removeZeros}",
      style: context.textTheme.primary.w700.copyWith(
        fontSize: 36.gw,
      ),
    );
  }

  Widget _buildInstructions(BuildContext context) {
    return Column(
      children: [
        AneText(
          "invite_friends_increase_withdrawal".tr(), // 邀请好友增加您的提现金额
          style: context.textTheme.secondary,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4.gw),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            children: [
              TextSpan(
                text: "withdraw_once_reach".tr(), // 达到后即可提现
                style: context.textTheme.secondary.ffAne,
              ),
              TextSpan(
                text: " ${GlobalConfig().systemConfig.currencySymbol}${widget.targetAmount.removeZeros}",
                style: context.textTheme.primary.w600.ffAne,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTaskButton(BuildContext context) {
    return CommonButton(
      title: 'complete_tasks_earn_rewards'.tr(), // 完成任务赚取奖励
      onPressed: widget.onTapSure,
    );
  }
}
