import 'package:flutter/material.dart';
import 'package:dotted_decoration/dotted_decoration.dart';
import 'package:wd/core/utils/screenUtil.dart';

class DottedBorderButton extends StatelessWidget {
  final String title;
  final VoidCallback? onPressed;
  final Color? textColor;
  final Color? borderColor;
  final Color? backgroundColor;
  final double? width;
  final double? height;
  final double? radius;
  final double? fontSize;
  final FontWeight? fontWeight;

  const DottedBorderButton({
    super.key,
    required this.title,
    this.onPressed,
    this.textColor,
    this.borderColor,
    this.backgroundColor = Colors.transparent,
    this.width,
    this.height,
    this.radius,
    this.fontSize,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height ?? 50.gw,
      decoration: DottedDecoration(
        shape: Shape.box,
        borderRadius: BorderRadius.circular(radius ?? 8.gw),
        color: borderColor ?? Theme.of(context).primaryColor,
        dash: const [5, 5],
        strokeWidth: 1,
      ),
      child: Material(
        borderRadius: BorderRadius.circular(radius ?? 8.gw),
        child: InkWell(
          borderRadius: BorderRadius.circular(radius ?? 8.gw),
          onTap: onPressed,
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                color: textColor ?? Theme.of(context).primaryColor,
                fontSize: fontSize ?? 16.gw,
                fontWeight: fontWeight ?? FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
